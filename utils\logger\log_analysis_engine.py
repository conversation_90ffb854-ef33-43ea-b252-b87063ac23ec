#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志分析引擎模块

提供高级的日志分析和统计功能
基于现有的性能监控机制，复用项目现有的分析基础设施
"""

import os
import sys
import time
import threading
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass
import logging
import re
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的模块
from utils.logger.log_aggregator import LogEntry
from utils.logger.log_deduplicator import DuplicatePattern
from utils.logger.process_detector import ProcessType


@dataclass
class LogPattern:
    """日志模式数据类"""
    pattern_id: str
    pattern_type: str
    regex_pattern: str
    description: str
    severity: str
    count: int = 0
    first_seen: float = 0.0
    last_seen: float = 0.0
    examples: List[str] = None


@dataclass
class AnalysisResult:
    """分析结果数据类"""
    analysis_type: str
    timestamp: float
    summary: Dict[str, Any]
    details: Dict[str, Any]
    recommendations: List[str]
    severity_score: float


class LogAnalysisEngine:
    """
    日志分析引擎
    
    基于现有的性能监控机制，提供高级的日志分析和统计功能
    """
    
    def __init__(self, analysis_window: float = 3600.0):
        """
        初始化日志分析引擎
        
        Args:
            analysis_window: 分析时间窗口（秒）
        """
        self.analysis_window = analysis_window
        
        # 日志存储
        self.log_buffer = []
        self.buffer_lock = threading.Lock()
        
        # 分析模式
        self.patterns = self._initialize_patterns()
        
        # 分析结果缓存
        self.analysis_cache = {}
        self.cache_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_analyzed': 0,
            'patterns_matched': 0,
            'analysis_runs': 0,
            'last_analysis': 0.0,
            'by_severity': defaultdict(int),
            'by_pattern_type': defaultdict(int),
            'by_process_type': defaultdict(int)
        }
    
    def _initialize_patterns(self) -> Dict[str, LogPattern]:
        """
        初始化日志模式
        
        Returns:
            Dict[str, LogPattern]: 模式字典
        """
        patterns = {}
        
        # 错误模式
        patterns['error_exception'] = LogPattern(
            pattern_id='error_exception',
            pattern_type='error',
            regex_pattern=r'(exception|error|failed|failure)',
            description='异常和错误日志',
            severity='high',
            examples=[]
        )
        
        # 性能模式
        patterns['slow_operation'] = LogPattern(
            pattern_id='slow_operation',
            pattern_type='performance',
            regex_pattern=r'(slow|timeout|延迟|超时)',
            description='性能问题日志',
            severity='medium',
            examples=[]
        )
        
        # 重复初始化模式
        patterns['duplicate_init'] = LogPattern(
            pattern_id='duplicate_init',
            pattern_type='duplicate',
            regex_pattern=r'(初始化完成|初始化成功|已注册|已加载)',
            description='重复初始化日志',
            severity='low',
            examples=[]
        )
        
        # 内存问题模式
        patterns['memory_issue'] = LogPattern(
            pattern_id='memory_issue',
            pattern_type='resource',
            regex_pattern=r'(memory|内存|out of memory|oom)',
            description='内存相关问题',
            severity='high',
            examples=[]
        )
        
        # 网络问题模式
        patterns['network_issue'] = LogPattern(
            pattern_id='network_issue',
            pattern_type='network',
            regex_pattern=r'(connection|网络|network|timeout|refused)',
            description='网络连接问题',
            severity='medium',
            examples=[]
        )
        
        # 数据库问题模式
        patterns['database_issue'] = LogPattern(
            pattern_id='database_issue',
            pattern_type='database',
            regex_pattern=r'(database|数据库|sql|query|connection pool)',
            description='数据库相关问题',
            severity='medium',
            examples=[]
        )
        
        return patterns
    
    def add_log_entry(self, log_entry: LogEntry):
        """
        添加日志条目进行分析
        
        Args:
            log_entry: 日志条目
        """
        with self.buffer_lock:
            self.log_buffer.append(log_entry)
            
            # 清理过期日志
            current_time = time.time()
            cutoff_time = current_time - self.analysis_window
            self.log_buffer = [
                entry for entry in self.log_buffer 
                if entry.timestamp > cutoff_time
            ]
        
        # 实时模式匹配
        self._match_patterns(log_entry)
        
        # 更新统计
        self.stats['total_analyzed'] += 1
        self.stats['by_process_type'][log_entry.process_type] += 1
    
    def _match_patterns(self, log_entry: LogEntry):
        """
        匹配日志模式
        
        Args:
            log_entry: 日志条目
        """
        message = log_entry.message.lower()
        
        for pattern_id, pattern in self.patterns.items():
            if re.search(pattern.regex_pattern, message, re.IGNORECASE):
                pattern.count += 1
                pattern.last_seen = log_entry.timestamp
                
                if pattern.first_seen == 0.0:
                    pattern.first_seen = log_entry.timestamp
                
                # 添加示例（最多保留5个）
                if pattern.examples is None:
                    pattern.examples = []
                
                if len(pattern.examples) < 5:
                    pattern.examples.append(log_entry.message)
                
                # 更新统计
                self.stats['patterns_matched'] += 1
                self.stats['by_severity'][pattern.severity] += 1
                self.stats['by_pattern_type'][pattern.pattern_type] += 1
    
    def analyze_logs(self, analysis_type: str = "comprehensive") -> AnalysisResult:
        """
        分析日志
        
        Args:
            analysis_type: 分析类型 ("comprehensive", "performance", "errors", "duplicates")
            
        Returns:
            AnalysisResult: 分析结果
        """
        current_time = time.time()
        
        # 检查缓存
        cache_key = f"{analysis_type}_{int(current_time // 300)}"  # 5分钟缓存
        with self.cache_lock:
            if cache_key in self.analysis_cache:
                return self.analysis_cache[cache_key]
        
        # 执行分析
        if analysis_type == "comprehensive":
            result = self._comprehensive_analysis()
        elif analysis_type == "performance":
            result = self._performance_analysis()
        elif analysis_type == "errors":
            result = self._error_analysis()
        elif analysis_type == "duplicates":
            result = self._duplicate_analysis()
        else:
            result = self._basic_analysis()
        
        # 缓存结果
        with self.cache_lock:
            self.analysis_cache[cache_key] = result
            
            # 清理过期缓存
            expired_keys = [
                key for key in self.analysis_cache.keys()
                if int(key.split('_')[-1]) < int(current_time // 300) - 12  # 保留1小时
            ]
            for key in expired_keys:
                del self.analysis_cache[key]
        
        # 更新统计
        self.stats['analysis_runs'] += 1
        self.stats['last_analysis'] = current_time
        
        return result
    
    def _comprehensive_analysis(self) -> AnalysisResult:
        """
        综合分析
        
        Returns:
            AnalysisResult: 分析结果
        """
        with self.buffer_lock:
            logs = self.log_buffer.copy()
        
        if not logs:
            return AnalysisResult(
                analysis_type="comprehensive",
                timestamp=time.time(),
                summary={"message": "没有日志数据可供分析"},
                details={},
                recommendations=[],
                severity_score=0.0
            )
        
        # 基本统计
        total_logs = len(logs)
        level_counts = Counter(log.level for log in logs)
        process_type_counts = Counter(log.process_type for log in logs)
        module_counts = Counter(log.module for log in logs)
        
        # 时间分布分析
        time_distribution = self._analyze_time_distribution(logs)
        
        # 模式分析
        pattern_summary = self._analyze_patterns()
        
        # 异常检测
        anomalies = self._detect_anomalies(logs)
        
        # 计算严重性评分
        severity_score = self._calculate_severity_score(pattern_summary, anomalies)
        
        # 生成建议
        recommendations = self._generate_recommendations(pattern_summary, anomalies, logs)
        
        summary = {
            "total_logs": total_logs,
            "time_range": f"{logs[0].timestamp:.1f} - {logs[-1].timestamp:.1f}",
            "level_distribution": dict(level_counts),
            "process_type_distribution": dict(process_type_counts),
            "top_modules": dict(module_counts.most_common(10)),
            "patterns_detected": len([p for p in self.patterns.values() if p.count > 0]),
            "anomalies_count": len(anomalies)
        }
        
        details = {
            "time_distribution": time_distribution,
            "pattern_analysis": pattern_summary,
            "anomalies": anomalies,
            "module_analysis": dict(module_counts)
        }
        
        return AnalysisResult(
            analysis_type="comprehensive",
            timestamp=time.time(),
            summary=summary,
            details=details,
            recommendations=recommendations,
            severity_score=severity_score
        )
    
    def _performance_analysis(self) -> AnalysisResult:
        """
        性能分析
        
        Returns:
            AnalysisResult: 分析结果
        """
        with self.buffer_lock:
            logs = [log for log in self.log_buffer if 'performance' in log.message.lower() or 
                   'slow' in log.message.lower() or 'timeout' in log.message.lower()]
        
        performance_patterns = {k: v for k, v in self.patterns.items() 
                              if v.pattern_type == 'performance'}
        
        summary = {
            "performance_logs_count": len(logs),
            "performance_patterns": len(performance_patterns),
            "slow_operations": sum(1 for log in logs if 'slow' in log.message.lower())
        }
        
        recommendations = []
        if len(logs) > 10:
            recommendations.append("检测到大量性能相关日志，建议优化系统性能")
        
        return AnalysisResult(
            analysis_type="performance",
            timestamp=time.time(),
            summary=summary,
            details={"performance_logs": [log.message for log in logs[:10]]},
            recommendations=recommendations,
            severity_score=min(len(logs) / 10.0, 10.0)
        )
    
    def _error_analysis(self) -> AnalysisResult:
        """
        错误分析
        
        Returns:
            AnalysisResult: 分析结果
        """
        with self.buffer_lock:
            error_logs = [log for log in self.log_buffer if log.level >= logging.ERROR]
        
        error_patterns = Counter()
        for log in error_logs:
            for pattern_id, pattern in self.patterns.items():
                if pattern.pattern_type == 'error' and re.search(pattern.regex_pattern, log.message, re.IGNORECASE):
                    error_patterns[pattern_id] += 1
        
        summary = {
            "total_errors": len(error_logs),
            "error_patterns": dict(error_patterns),
            "error_rate": len(error_logs) / max(1, len(self.log_buffer)) * 100
        }
        
        recommendations = []
        if len(error_logs) > 5:
            recommendations.append("错误日志数量较多，建议检查系统稳定性")
        
        return AnalysisResult(
            analysis_type="errors",
            timestamp=time.time(),
            summary=summary,
            details={"recent_errors": [log.message for log in error_logs[-5:]]},
            recommendations=recommendations,
            severity_score=min(len(error_logs), 10.0)
        )
    
    def _duplicate_analysis(self) -> AnalysisResult:
        """
        重复日志分析
        
        Returns:
            AnalysisResult: 分析结果
        """
        duplicate_pattern = self.patterns.get('duplicate_init')
        
        summary = {
            "duplicate_init_count": duplicate_pattern.count if duplicate_pattern else 0,
            "duplicate_rate": 0.0
        }
        
        if duplicate_pattern and len(self.log_buffer) > 0:
            summary["duplicate_rate"] = duplicate_pattern.count / len(self.log_buffer) * 100
        
        recommendations = []
        if duplicate_pattern and duplicate_pattern.count > 10:
            recommendations.append("检测到大量重复初始化日志，建议启用日志去重功能")
        
        return AnalysisResult(
            analysis_type="duplicates",
            timestamp=time.time(),
            summary=summary,
            details={"duplicate_examples": duplicate_pattern.examples if duplicate_pattern else []},
            recommendations=recommendations,
            severity_score=min(duplicate_pattern.count / 10.0, 5.0) if duplicate_pattern else 0.0
        )
    
    def _basic_analysis(self) -> AnalysisResult:
        """
        基本分析
        
        Returns:
            AnalysisResult: 分析结果
        """
        with self.buffer_lock:
            total_logs = len(self.log_buffer)
        
        summary = {"total_logs": total_logs}
        
        return AnalysisResult(
            analysis_type="basic",
            timestamp=time.time(),
            summary=summary,
            details={},
            recommendations=[],
            severity_score=0.0
        )
    
    def _analyze_time_distribution(self, logs: List[LogEntry]) -> Dict[str, Any]:
        """
        分析时间分布
        
        Args:
            logs: 日志列表
            
        Returns:
            Dict[str, Any]: 时间分布分析结果
        """
        if not logs:
            return {}
        
        # 按小时分组
        hourly_counts = defaultdict(int)
        for log in logs:
            hour = int(log.timestamp) // 3600 * 3600
            hourly_counts[hour] += 1
        
        return {
            "hourly_distribution": dict(hourly_counts),
            "peak_hour": max(hourly_counts.items(), key=lambda x: x[1])[0] if hourly_counts else None,
            "total_hours": len(hourly_counts)
        }
    
    def _analyze_patterns(self) -> Dict[str, Any]:
        """
        分析模式
        
        Returns:
            Dict[str, Any]: 模式分析结果
        """
        active_patterns = {k: v for k, v in self.patterns.items() if v.count > 0}
        
        return {
            "total_patterns": len(self.patterns),
            "active_patterns": len(active_patterns),
            "pattern_details": {
                k: {
                    "count": v.count,
                    "severity": v.severity,
                    "type": v.pattern_type,
                    "description": v.description
                }
                for k, v in active_patterns.items()
            }
        }
    
    def _detect_anomalies(self, logs: List[LogEntry]) -> List[Dict[str, Any]]:
        """
        检测异常
        
        Args:
            logs: 日志列表
            
        Returns:
            List[Dict[str, Any]]: 异常列表
        """
        anomalies = []
        
        # 检测日志量突增
        if len(logs) > 1000:
            anomalies.append({
                "type": "high_volume",
                "description": f"日志量异常高: {len(logs)} 条",
                "severity": "medium"
            })
        
        # 检测错误率异常
        error_count = sum(1 for log in logs if log.level >= logging.ERROR)
        error_rate = error_count / len(logs) * 100 if logs else 0
        
        if error_rate > 10:
            anomalies.append({
                "type": "high_error_rate",
                "description": f"错误率异常高: {error_rate:.1f}%",
                "severity": "high"
            })
        
        return anomalies
    
    def _calculate_severity_score(self, pattern_summary: Dict[str, Any], 
                                 anomalies: List[Dict[str, Any]]) -> float:
        """
        计算严重性评分
        
        Args:
            pattern_summary: 模式摘要
            anomalies: 异常列表
            
        Returns:
            float: 严重性评分 (0-10)
        """
        score = 0.0
        
        # 基于模式的评分
        for pattern_info in pattern_summary.get("pattern_details", {}).values():
            if pattern_info["severity"] == "high":
                score += pattern_info["count"] * 0.5
            elif pattern_info["severity"] == "medium":
                score += pattern_info["count"] * 0.2
            else:
                score += pattern_info["count"] * 0.1
        
        # 基于异常的评分
        for anomaly in anomalies:
            if anomaly["severity"] == "high":
                score += 3.0
            elif anomaly["severity"] == "medium":
                score += 1.5
            else:
                score += 0.5
        
        return min(score, 10.0)
    
    def _generate_recommendations(self, pattern_summary: Dict[str, Any], 
                                 anomalies: List[Dict[str, Any]], 
                                 logs: List[LogEntry]) -> List[str]:
        """
        生成建议
        
        Args:
            pattern_summary: 模式摘要
            anomalies: 异常列表
            logs: 日志列表
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        # 基于模式的建议
        duplicate_count = 0
        for pattern_id, pattern_info in pattern_summary.get("pattern_details", {}).items():
            if pattern_info["type"] == "duplicate" and pattern_info["count"] > 10:
                duplicate_count += pattern_info["count"]
        
        if duplicate_count > 50:
            recommendations.append("启用分布式日志系统以减少重复日志")
        
        # 基于异常的建议
        for anomaly in anomalies:
            if anomaly["type"] == "high_error_rate":
                recommendations.append("检查系统错误，提高系统稳定性")
            elif anomaly["type"] == "high_volume":
                recommendations.append("优化日志级别配置，减少不必要的日志输出")
        
        # 基于工作进程的建议
        worker_logs = sum(1 for log in logs if log.process_type == ProcessType.WORKER.value)
        if worker_logs > len(logs) * 0.7:
            recommendations.append("工作进程日志占比过高，建议调整工作进程的日志级别")
        
        return recommendations
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """
        获取分析统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        return {
            'total_analyzed': self.stats['total_analyzed'],
            'patterns_matched': self.stats['patterns_matched'],
            'analysis_runs': self.stats['analysis_runs'],
            'last_analysis': self.stats['last_analysis'],
            'buffer_size': len(self.log_buffer),
            'cache_size': len(self.analysis_cache),
            'by_severity': dict(self.stats['by_severity']),
            'by_pattern_type': dict(self.stats['by_pattern_type']),
            'by_process_type': dict(self.stats['by_process_type'])
        }
    
    def clear_analysis_data(self):
        """清空分析数据"""
        with self.buffer_lock:
            self.log_buffer.clear()
        
        with self.cache_lock:
            self.analysis_cache.clear()
        
        # 重置模式计数
        for pattern in self.patterns.values():
            pattern.count = 0
            pattern.first_seen = 0.0
            pattern.last_seen = 0.0
            if pattern.examples:
                pattern.examples.clear()


# 全局分析引擎实例
_analysis_engine_instance = None


def get_log_analysis_engine() -> LogAnalysisEngine:
    """
    获取全局日志分析引擎实例
    
    Returns:
        LogAnalysisEngine: 分析引擎实例
    """
    global _analysis_engine_instance
    if _analysis_engine_instance is None:
        _analysis_engine_instance = LogAnalysisEngine()
    return _analysis_engine_instance
