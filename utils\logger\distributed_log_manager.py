#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分布式日志管理器模块

整合现有的setup_unified_logging功能，提供分布式日志管理
复用项目现有的日志基础设施，添加进程感知和智能去重功能
"""

import os
import sys
import logging
from typing import Dict, List, Optional, Any
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的日志模块
from utils.logger.config import LogTarget, LOG_LEVELS
from utils.logger.manager import setup_unified_logging, get_unified_logger
from utils.logger.process_detector import get_process_detector, ProcessType
from utils.logger.log_aggregator import get_log_aggregator, LogEntry
from utils.logger.log_deduplicator import get_log_deduplicator
from utils.logger.filters import TargetFilter


class DistributedLogHandler(logging.Handler):
    """
    分布式日志处理器
    
    集成进程检测、日志聚合和去重功能
    """
    
    def __init__(self, enable_deduplication: bool = True):
        """
        初始化分布式日志处理器
        
        Args:
            enable_deduplication: 是否启用去重功能
        """
        super().__init__()
        self.enable_deduplication = enable_deduplication
        self.process_detector = get_process_detector()
        self.aggregator = get_log_aggregator()
        self.deduplicator = get_log_deduplicator() if enable_deduplication else None
        
        # 启动聚合器
        self.aggregator.start()
    
    def emit(self, record: logging.LogRecord):
        """
        处理日志记录
        
        Args:
            record: 日志记录对象
        """
        try:
            # 检查是否应该抑制此日志
            if self._should_suppress_log(record):
                return
            
            # 如果启用去重，检查是否重复
            if self.deduplicator:
                log_entry = self._create_log_entry(record)
                is_duplicate, reason = self.deduplicator.is_duplicate(log_entry)
                
                if is_duplicate:
                    # 可以选择记录去重信息或完全忽略
                    if reason and "第1次" not in reason:  # 只在第一次重复时记录
                        return
            
            # 提交到聚合器
            self.aggregator.submit_log(record)
            
        except Exception:
            # 处理异常，避免影响主程序
            self.handleError(record)
    
    def _should_suppress_log(self, record: logging.LogRecord) -> bool:
        """
        检查是否应该抑制此日志
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 如果应该抑制返回True
        """
        # 基于进程类型的抑制策略
        if self.process_detector.should_suppress_init_logs():
            # 工作进程抑制初始化日志
            message = record.getMessage().lower()
            init_keywords = ['初始化完成', '初始化成功', '已注册', '已加载', '版本:', '提供']
            
            if any(keyword in message for keyword in init_keywords):
                return True
            
            # 工作进程只保留WARNING及以上级别
            if record.levelno < logging.WARNING:
                return True
        
        return False
    
    def _create_log_entry(self, record: logging.LogRecord) -> LogEntry:
        """
        创建日志条目
        
        Args:
            record: 日志记录对象
            
        Returns:
            LogEntry: 日志条目对象
        """
        return LogEntry(
            timestamp=record.created,
            level=record.levelno,
            message=record.getMessage(),
            module=record.name,
            function=record.funcName,
            line_number=record.lineno,
            process_id=os.getpid(),
            process_type=self.process_detector.detect_process_type().value,
            thread_id=record.thread,
            extra_data=getattr(record, 'extra_data', None)
        )


class DistributedLogManager:
    """
    分布式日志管理器
    
    整合现有的setup_unified_logging功能，提供分布式日志管理
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化分布式日志管理器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.process_detector = get_process_detector()
        self.aggregator = get_log_aggregator()
        self.deduplicator = get_log_deduplicator()
        self.distributed_handler = None
        self.is_setup = False
        
        # 配置参数
        self.config = {
            'enable_deduplication': True,
            'enable_process_awareness': True,
            'enable_aggregation': True,
            'log_level_override': None
        }
    
    def setup_distributed_logging(self,
                                 enable_deduplication: bool = True,
                                 enable_process_awareness: bool = True,
                                 enable_aggregation: bool = True,
                                 **kwargs):
        """
        设置分布式日志系统
        
        Args:
            enable_deduplication: 是否启用去重功能
            enable_process_awareness: 是否启用进程感知
            enable_aggregation: 是否启用日志聚合
            **kwargs: 传递给setup_unified_logging的其他参数
        """
        if self.is_setup:
            return
        
        # 更新配置
        self.config.update({
            'enable_deduplication': enable_deduplication,
            'enable_process_awareness': enable_process_awareness,
            'enable_aggregation': enable_aggregation
        })
        
        # 基于进程类型调整日志级别
        if enable_process_awareness:
            recommended_level = self.process_detector.get_recommended_log_level()
            if 'log_level' not in kwargs:
                kwargs['log_level'] = recommended_level.lower()
        
        # 调用原有的统一日志设置
        setup_unified_logging(**kwargs)
        
        # 如果启用聚合，添加分布式处理器
        if enable_aggregation:
            self.distributed_handler = DistributedLogHandler(enable_deduplication)
            
            # 添加文件输出处理器
            self.aggregator.add_handler(self._file_output_handler)
            
            # 如果是主进程，也添加控制台输出处理器
            if self.process_detector.is_main_process():
                self.aggregator.add_handler(self._console_output_handler)
        
        self.is_setup = True
    
    def _file_output_handler(self, log_entries: List[LogEntry]):
        """
        文件输出处理器
        
        Args:
            log_entries: 日志条目列表
        """
        try:
            # 获取文件日志记录器
            file_logger = logging.getLogger('distributed_file')
            
            for entry in log_entries:
                # 创建日志记录
                record = logging.LogRecord(
                    name=entry.module,
                    level=entry.level,
                    pathname='',
                    lineno=entry.line_number,
                    msg=entry.message,
                    args=(),
                    exc_info=None,
                    func=entry.function
                )
                record.created = entry.timestamp
                record.thread = entry.thread_id
                record.process = entry.process_id
                
                # 添加进程类型信息
                record.process_type = entry.process_type
                
                # 输出到文件
                for handler in file_logger.handlers:
                    if isinstance(handler.filters[0] if handler.filters else None, TargetFilter):
                        target_filter = handler.filters[0]
                        if target_filter.target_type == LogTarget.FILE:
                            handler.emit(record)
                            
        except Exception as e:
            print(f"文件输出处理器异常: {e}")
    
    def _console_output_handler(self, log_entries: List[LogEntry]):
        """
        控制台输出处理器（仅主进程）
        
        Args:
            log_entries: 日志条目列表
        """
        try:
            # 只有主进程才输出到控制台
            if not self.process_detector.is_main_process():
                return
            
            # 获取控制台日志记录器
            console_logger = logging.getLogger('distributed_console')
            
            for entry in log_entries:
                # 过滤低级别日志
                if entry.level < logging.INFO:
                    continue
                
                # 创建日志记录
                record = logging.LogRecord(
                    name=entry.module,
                    level=entry.level,
                    pathname='',
                    lineno=entry.line_number,
                    msg=entry.message,
                    args=(),
                    exc_info=None,
                    func=entry.function
                )
                record.created = entry.timestamp
                record.thread = entry.thread_id
                record.process = entry.process_id
                
                # 输出到控制台
                for handler in console_logger.handlers:
                    if isinstance(handler.filters[0] if handler.filters else None, TargetFilter):
                        target_filter = handler.filters[0]
                        if target_filter.target_type == LogTarget.CONSOLE:
                            handler.emit(record)
                            
        except Exception as e:
            print(f"控制台输出处理器异常: {e}")
    
    def get_enhanced_logger(self, name: str, **kwargs):
        """
        获取增强的日志记录器
        
        Args:
            name: 日志记录器名称
            **kwargs: 其他参数
            
        Returns:
            增强的日志记录器
        """
        # 获取原有的统一日志记录器
        logger = get_unified_logger(name, **kwargs)
        
        # 如果启用聚合，添加分布式处理器
        if self.config['enable_aggregation'] and self.distributed_handler:
            logger.addHandler(self.distributed_handler)
        
        return logger
    
    def get_system_stats(self) -> Dict[str, Any]:
        """
        获取系统统计信息
        
        Returns:
            Dict[str, Any]: 系统统计信息
        """
        stats = {
            'process_info': self.process_detector.get_process_info(),
            'config': self.config.copy(),
            'is_setup': self.is_setup
        }
        
        if self.config['enable_aggregation']:
            stats['aggregator'] = self.aggregator.get_stats()
        
        if self.config['enable_deduplication']:
            stats['deduplicator'] = self.deduplicator.get_stats()
        
        return stats
    
    def shutdown(self):
        """关闭分布式日志系统"""
        if self.aggregator:
            self.aggregator.stop()
        
        self.is_setup = False


# 全局管理器实例
_manager_instance = None


def get_distributed_log_manager() -> DistributedLogManager:
    """
    获取全局分布式日志管理器实例
    
    Returns:
        DistributedLogManager: 管理器实例
    """
    global _manager_instance
    if _manager_instance is None:
        _manager_instance = DistributedLogManager()
    return _manager_instance


def setup_distributed_logging(**kwargs):
    """
    便利函数：设置分布式日志系统
    
    Args:
        **kwargs: 配置参数
    """
    manager = get_distributed_log_manager()
    manager.setup_distributed_logging(**kwargs)


def get_enhanced_logger(name: str, **kwargs):
    """
    便利函数：获取增强的日志记录器
    
    Args:
        name: 日志记录器名称
        **kwargs: 其他参数
        
    Returns:
        增强的日志记录器
    """
    manager = get_distributed_log_manager()
    return manager.get_enhanced_logger(name, **kwargs)
