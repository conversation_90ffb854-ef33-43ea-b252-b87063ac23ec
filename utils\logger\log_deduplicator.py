#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志去重引擎模块

提供智能的日志去重功能，利用现有的缓存机制
复用项目现有的性能监控和缓存基础设施
"""

import os
import sys
import time
import hashlib
from typing import Dict, List, Optional, Set, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的模块
from utils.logger.log_aggregator import LogEntry
from utils.logger.process_detector import ProcessType


@dataclass
class DuplicatePattern:
    """重复模式数据类"""
    signature: str
    first_seen: float
    last_seen: float
    count: int
    process_types: Set[str]
    modules: Set[str]
    sample_message: str


class LogDeduplicator:
    """
    日志去重引擎
    
    利用现有的缓存机制，提供智能的日志去重功能
    支持基于内容、时间窗口和进程类型的去重策略
    """
    
    def __init__(self, 
                 cache_size: int = 10000,
                 time_window: float = 60.0,
                 similarity_threshold: float = 0.8):
        """
        初始化去重引擎
        
        Args:
            cache_size: 缓存大小
            time_window: 时间窗口（秒）
            similarity_threshold: 相似度阈值
        """
        self.cache_size = cache_size
        self.time_window = time_window
        self.similarity_threshold = similarity_threshold
        
        # 日志签名缓存
        self.signature_cache: Dict[str, DuplicatePattern] = {}
        
        # 时间窗口缓存
        self.time_window_cache = deque(maxlen=cache_size)
        
        # 相似度缓存
        self.similarity_cache: Dict[str, List[str]] = defaultdict(list)
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'duplicates_found': 0,
            'unique_logs': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 去重规则配置
        self.dedup_rules = {
            # 初始化日志去重规则
            'init_logs': {
                'keywords': ['初始化完成', '初始化成功', '已注册', '已加载', '版本:', '提供'],
                'max_duplicates': 1,  # 每种初始化日志最多保留1条
                'process_aware': True  # 区分不同进程类型
            },
            # 模块导入日志去重规则
            'import_logs': {
                'keywords': ['已导入', 'import', '模块加载'],
                'max_duplicates': 1,
                'process_aware': True
            },
            # 配置日志去重规则
            'config_logs': {
                'keywords': ['配置', 'settings', 'config'],
                'max_duplicates': 2,
                'process_aware': False
            }
        }
    
    def is_duplicate(self, log_entry: LogEntry) -> Tuple[bool, Optional[str]]:
        """
        检查日志是否重复
        
        Args:
            log_entry: 日志条目
            
        Returns:
            Tuple[bool, Optional[str]]: (是否重复, 重复原因)
        """
        self.stats['total_processed'] += 1
        
        # 生成日志签名
        signature = self._generate_signature(log_entry)
        
        # 检查精确匹配
        if signature in self.signature_cache:
            pattern = self.signature_cache[signature]
            
            # 更新模式信息
            pattern.last_seen = log_entry.timestamp
            pattern.count += 1
            pattern.process_types.add(log_entry.process_type)
            pattern.modules.add(log_entry.module)
            
            self.stats['duplicates_found'] += 1
            self.stats['cache_hits'] += 1
            
            return True, f"精确匹配 (出现{pattern.count}次)"
        
        # 检查基于规则的去重
        duplicate_reason = self._check_rule_based_duplicate(log_entry)
        if duplicate_reason:
            self.stats['duplicates_found'] += 1
            return True, duplicate_reason
        
        # 检查时间窗口内的相似日志
        similar_reason = self._check_time_window_similarity(log_entry)
        if similar_reason:
            self.stats['duplicates_found'] += 1
            return True, similar_reason
        
        # 不是重复日志，添加到缓存
        self._add_to_cache(log_entry, signature)
        self.stats['unique_logs'] += 1
        self.stats['cache_misses'] += 1
        
        return False, None
    
    def _generate_signature(self, log_entry: LogEntry) -> str:
        """
        生成日志签名
        
        Args:
            log_entry: 日志条目
            
        Returns:
            str: 日志签名
        """
        # 基于模块、函数和消息内容生成签名
        content = f"{log_entry.module}:{log_entry.function}:{log_entry.message}"
        
        # 对于工作进程，包含进程类型信息
        if log_entry.process_type == ProcessType.WORKER.value:
            content = f"{log_entry.process_type}:{content}"
        
        # 生成MD5哈希
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _check_rule_based_duplicate(self, log_entry: LogEntry) -> Optional[str]:
        """
        基于规则检查重复
        
        Args:
            log_entry: 日志条目
            
        Returns:
            Optional[str]: 重复原因，如果不重复返回None
        """
        message = log_entry.message.lower()
        
        for rule_name, rule_config in self.dedup_rules.items():
            # 检查是否匹配关键词
            if any(keyword in message for keyword in rule_config['keywords']):
                # 生成规则特定的签名
                if rule_config['process_aware']:
                    rule_signature = f"{rule_name}:{log_entry.process_type}:{log_entry.module}"
                else:
                    rule_signature = f"{rule_name}:{log_entry.module}"
                
                # 检查是否超过最大重复次数
                if rule_signature in self.signature_cache:
                    pattern = self.signature_cache[rule_signature]
                    if pattern.count >= rule_config['max_duplicates']:
                        pattern.count += 1
                        pattern.last_seen = log_entry.timestamp
                        return f"规则去重 ({rule_name}, 第{pattern.count}次)"
                else:
                    # 创建新的模式
                    self.signature_cache[rule_signature] = DuplicatePattern(
                        signature=rule_signature,
                        first_seen=log_entry.timestamp,
                        last_seen=log_entry.timestamp,
                        count=1,
                        process_types={log_entry.process_type},
                        modules={log_entry.module},
                        sample_message=log_entry.message
                    )
        
        return None
    
    def _check_time_window_similarity(self, log_entry: LogEntry) -> Optional[str]:
        """
        检查时间窗口内的相似日志
        
        Args:
            log_entry: 日志条目
            
        Returns:
            Optional[str]: 重复原因，如果不重复返回None
        """
        current_time = log_entry.timestamp
        
        # 清理过期的时间窗口缓存
        while (self.time_window_cache and 
               current_time - self.time_window_cache[0][0] > self.time_window):
            self.time_window_cache.popleft()
        
        # 检查时间窗口内的相似日志
        for timestamp, cached_entry in self.time_window_cache:
            if (log_entry.module == cached_entry.module and
                log_entry.function == cached_entry.function and
                self._calculate_similarity(log_entry.message, cached_entry.message) > self.similarity_threshold):
                
                return f"时间窗口相似 (与{timestamp:.1f}秒前的日志相似)"
        
        return None
    
    def _calculate_similarity(self, message1: str, message2: str) -> float:
        """
        计算两个消息的相似度
        
        Args:
            message1: 消息1
            message2: 消息2
            
        Returns:
            float: 相似度 (0-1)
        """
        # 简单的基于词汇的相似度计算
        words1 = set(message1.lower().split())
        words2 = set(message2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _add_to_cache(self, log_entry: LogEntry, signature: str):
        """
        添加日志到缓存
        
        Args:
            log_entry: 日志条目
            signature: 日志签名
        """
        # 添加到签名缓存
        self.signature_cache[signature] = DuplicatePattern(
            signature=signature,
            first_seen=log_entry.timestamp,
            last_seen=log_entry.timestamp,
            count=1,
            process_types={log_entry.process_type},
            modules={log_entry.module},
            sample_message=log_entry.message
        )
        
        # 添加到时间窗口缓存
        self.time_window_cache.append((log_entry.timestamp, log_entry))
        
        # 清理过期缓存
        self._cleanup_cache()
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        if len(self.signature_cache) > self.cache_size:
            # 移除最旧的缓存项
            oldest_signatures = sorted(
                self.signature_cache.items(),
                key=lambda x: x[1].first_seen
            )[:len(self.signature_cache) - self.cache_size + 100]
            
            for signature, _ in oldest_signatures:
                del self.signature_cache[signature]
    
    def get_duplicate_patterns(self) -> List[DuplicatePattern]:
        """
        获取重复模式列表
        
        Returns:
            List[DuplicatePattern]: 重复模式列表
        """
        return [pattern for pattern in self.signature_cache.values() if pattern.count > 1]
    
    def get_stats(self) -> Dict:
        """
        获取去重统计信息
        
        Returns:
            Dict: 统计信息字典
        """
        return {
            'total_processed': self.stats['total_processed'],
            'duplicates_found': self.stats['duplicates_found'],
            'unique_logs': self.stats['unique_logs'],
            'cache_hits': self.stats['cache_hits'],
            'cache_misses': self.stats['cache_misses'],
            'cache_size': len(self.signature_cache),
            'duplicate_rate': (self.stats['duplicates_found'] / max(1, self.stats['total_processed'])) * 100
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.signature_cache.clear()
        self.time_window_cache.clear()
        self.similarity_cache.clear()
    
    def clear_stats(self):
        """清空统计信息"""
        self.stats = {
            'total_processed': 0,
            'duplicates_found': 0,
            'unique_logs': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }


# 全局去重引擎实例
_deduplicator_instance = None


def get_log_deduplicator() -> LogDeduplicator:
    """
    获取全局日志去重引擎实例
    
    Returns:
        LogDeduplicator: 去重引擎实例
    """
    global _deduplicator_instance
    if _deduplicator_instance is None:
        _deduplicator_instance = LogDeduplicator()
    return _deduplicator_instance
