#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
兼容性适配器模块

确保现有代码无需修改即可使用新的分布式日志系统
提供向后兼容的API和渐进式迁移支持
"""

import os
import sys
import logging
import warnings
from typing import Dict, List, Optional, Any, Union
import functools

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的日志模块
from utils.logger.manager import setup_unified_logging as original_setup_unified_logging
from utils.logger.manager import get_unified_logger as original_get_unified_logger
from utils.logger.config import LogTarget

# 导入新的分布式日志模块
from utils.logger.distributed_log_manager import (
    get_distributed_log_manager, 
    setup_distributed_logging as new_setup_distributed_logging,
    get_enhanced_logger as new_get_enhanced_logger
)
from utils.logger.process_detector import get_process_detector


class CompatibilityAdapter:
    """
    兼容性适配器
    
    提供向后兼容的API，确保现有代码无需修改即可使用新系统
    """
    
    def __init__(self):
        """初始化兼容性适配器"""
        self.migration_mode = "gradual"  # "disabled", "gradual", "full"
        self.compatibility_warnings = True
        self.fallback_enabled = True
        
        # 迁移统计
        self.migration_stats = {
            'old_api_calls': 0,
            'new_api_calls': 0,
            'fallback_calls': 0,
            'warnings_issued': 0
        }
        
        # 获取组件
        self.distributed_manager = get_distributed_log_manager()
        self.process_detector = get_process_detector()
        
        # 检测环境变量配置
        self._load_config_from_env()
    
    def _load_config_from_env(self):
        """从环境变量加载配置"""
        # 迁移模式
        migration_mode = os.environ.get("LOG_MIGRATION_MODE", "gradual").lower()
        if migration_mode in ["disabled", "gradual", "full"]:
            self.migration_mode = migration_mode
        
        # 兼容性警告
        self.compatibility_warnings = os.environ.get(
            "LOG_COMPATIBILITY_WARNINGS", "true"
        ).lower() == "true"
        
        # 回退功能
        self.fallback_enabled = os.environ.get(
            "LOG_FALLBACK_ENABLED", "true"
        ).lower() == "true"
    
    def setup_unified_logging(self, *args, **kwargs):
        """
        兼容的统一日志设置函数
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
        """
        self.migration_stats['old_api_calls'] += 1
        
        if self.migration_mode == "disabled":
            # 禁用新系统，使用原有系统
            return original_setup_unified_logging(*args, **kwargs)
        
        elif self.migration_mode == "gradual":
            # 渐进式迁移：同时设置新旧系统
            try:
                # 发出兼容性警告
                if self.compatibility_warnings:
                    warnings.warn(
                        "setup_unified_logging 正在迁移到分布式日志系统。"
                        "建议使用 setup_distributed_logging。",
                        DeprecationWarning,
                        stacklevel=2
                    )
                    self.migration_stats['warnings_issued'] += 1
                
                # 设置新系统
                self.distributed_manager.setup_distributed_logging(**kwargs)
                self.migration_stats['new_api_calls'] += 1
                
                # 同时设置原有系统作为备份
                if self.fallback_enabled:
                    original_setup_unified_logging(*args, **kwargs)
                    self.migration_stats['fallback_calls'] += 1
                
            except Exception as e:
                # 新系统失败，回退到原有系统
                if self.fallback_enabled:
                    print(f"分布式日志系统设置失败，回退到原有系统: {e}")
                    original_setup_unified_logging(*args, **kwargs)
                    self.migration_stats['fallback_calls'] += 1
                else:
                    raise
        
        elif self.migration_mode == "full":
            # 完全迁移：只使用新系统
            self.distributed_manager.setup_distributed_logging(**kwargs)
            self.migration_stats['new_api_calls'] += 1
    
    def get_unified_logger(self, name: str, **kwargs):
        """
        兼容的统一日志记录器获取函数
        
        Args:
            name: 日志记录器名称
            **kwargs: 其他参数
            
        Returns:
            日志记录器实例
        """
        self.migration_stats['old_api_calls'] += 1
        
        if self.migration_mode == "disabled":
            # 禁用新系统，使用原有系统
            return original_get_unified_logger(name, **kwargs)
        
        elif self.migration_mode == "gradual":
            # 渐进式迁移：优先使用新系统
            try:
                # 发出兼容性警告
                if self.compatibility_warnings:
                    warnings.warn(
                        "get_unified_logger 正在迁移到分布式日志系统。"
                        "建议使用 get_enhanced_logger。",
                        DeprecationWarning,
                        stacklevel=2
                    )
                    self.migration_stats['warnings_issued'] += 1
                
                # 尝试获取增强日志记录器
                logger = self.distributed_manager.get_enhanced_logger(name, **kwargs)
                self.migration_stats['new_api_calls'] += 1
                return logger
                
            except Exception as e:
                # 新系统失败，回退到原有系统
                if self.fallback_enabled:
                    print(f"获取增强日志记录器失败，回退到原有系统: {e}")
                    logger = original_get_unified_logger(name, **kwargs)
                    self.migration_stats['fallback_calls'] += 1
                    return logger
                else:
                    raise
        
        elif self.migration_mode == "full":
            # 完全迁移：只使用新系统
            logger = self.distributed_manager.get_enhanced_logger(name, **kwargs)
            self.migration_stats['new_api_calls'] += 1
            return logger
    
    def create_migration_wrapper(self, old_func, new_func, func_name: str):
        """
        创建迁移包装器
        
        Args:
            old_func: 原有函数
            new_func: 新函数
            func_name: 函数名称
            
        Returns:
            包装后的函数
        """
        @functools.wraps(old_func)
        def wrapper(*args, **kwargs):
            self.migration_stats['old_api_calls'] += 1
            
            if self.migration_mode == "disabled":
                return old_func(*args, **kwargs)
            
            elif self.migration_mode == "gradual":
                try:
                    if self.compatibility_warnings:
                        warnings.warn(
                            f"{func_name} 正在迁移到分布式日志系统。",
                            DeprecationWarning,
                            stacklevel=2
                        )
                        self.migration_stats['warnings_issued'] += 1
                    
                    result = new_func(*args, **kwargs)
                    self.migration_stats['new_api_calls'] += 1
                    return result
                    
                except Exception as e:
                    if self.fallback_enabled:
                        print(f"{func_name} 新系统失败，回退: {e}")
                        result = old_func(*args, **kwargs)
                        self.migration_stats['fallback_calls'] += 1
                        return result
                    else:
                        raise
            
            elif self.migration_mode == "full":
                result = new_func(*args, **kwargs)
                self.migration_stats['new_api_calls'] += 1
                return result
        
        return wrapper
    
    def get_migration_stats(self) -> Dict[str, Any]:
        """
        获取迁移统计信息
        
        Returns:
            Dict[str, Any]: 迁移统计信息
        """
        total_calls = sum(self.migration_stats.values())
        
        return {
            'migration_mode': self.migration_mode,
            'compatibility_warnings': self.compatibility_warnings,
            'fallback_enabled': self.fallback_enabled,
            'stats': self.migration_stats.copy(),
            'total_calls': total_calls,
            'new_system_usage_rate': (
                self.migration_stats['new_api_calls'] / max(1, total_calls)
            ) * 100,
            'fallback_rate': (
                self.migration_stats['fallback_calls'] / max(1, total_calls)
            ) * 100
        }
    
    def set_migration_mode(self, mode: str):
        """
        设置迁移模式
        
        Args:
            mode: 迁移模式 ("disabled", "gradual", "full")
        """
        if mode in ["disabled", "gradual", "full"]:
            self.migration_mode = mode
        else:
            raise ValueError(f"无效的迁移模式: {mode}")
    
    def enable_compatibility_warnings(self, enabled: bool = True):
        """
        启用/禁用兼容性警告
        
        Args:
            enabled: 是否启用警告
        """
        self.compatibility_warnings = enabled
    
    def enable_fallback(self, enabled: bool = True):
        """
        启用/禁用回退功能
        
        Args:
            enabled: 是否启用回退
        """
        self.fallback_enabled = enabled
    
    def test_compatibility(self) -> Dict[str, Any]:
        """
        测试兼容性
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        test_results = {
            'setup_function': False,
            'logger_function': False,
            'distributed_system': False,
            'fallback_system': False,
            'errors': []
        }
        
        try:
            # 测试设置函数
            self.setup_unified_logging(log_level='info')
            test_results['setup_function'] = True
        except Exception as e:
            test_results['errors'].append(f"设置函数测试失败: {e}")
        
        try:
            # 测试日志记录器获取
            logger = self.get_unified_logger('test_logger')
            logger.info("兼容性测试消息")
            test_results['logger_function'] = True
        except Exception as e:
            test_results['errors'].append(f"日志记录器测试失败: {e}")
        
        try:
            # 测试分布式系统
            if self.distributed_manager.is_setup:
                test_results['distributed_system'] = True
        except Exception as e:
            test_results['errors'].append(f"分布式系统测试失败: {e}")
        
        try:
            # 测试回退系统
            original_logger = original_get_unified_logger('test_fallback')
            original_logger.info("回退系统测试消息")
            test_results['fallback_system'] = True
        except Exception as e:
            test_results['errors'].append(f"回退系统测试失败: {e}")
        
        return test_results


# 全局兼容性适配器实例
_compatibility_adapter_instance = None


def get_compatibility_adapter() -> CompatibilityAdapter:
    """
    获取全局兼容性适配器实例
    
    Returns:
        CompatibilityAdapter: 兼容性适配器实例
    """
    global _compatibility_adapter_instance
    if _compatibility_adapter_instance is None:
        _compatibility_adapter_instance = CompatibilityAdapter()
    return _compatibility_adapter_instance


# 兼容性API函数
def setup_unified_logging(*args, **kwargs):
    """
    兼容的统一日志设置函数
    
    这是一个向后兼容的API，会根据迁移模式选择使用新旧系统
    """
    adapter = get_compatibility_adapter()
    return adapter.setup_unified_logging(*args, **kwargs)


def get_unified_logger(name: str, **kwargs):
    """
    兼容的统一日志记录器获取函数
    
    这是一个向后兼容的API，会根据迁移模式选择使用新旧系统
    
    Args:
        name: 日志记录器名称
        **kwargs: 其他参数
        
    Returns:
        日志记录器实例
    """
    adapter = get_compatibility_adapter()
    return adapter.get_unified_logger(name, **kwargs)


# 迁移辅助函数
def migrate_to_distributed_logging(enable_warnings: bool = True, 
                                  enable_fallback: bool = True):
    """
    迁移到分布式日志系统
    
    Args:
        enable_warnings: 是否启用兼容性警告
        enable_fallback: 是否启用回退功能
    """
    adapter = get_compatibility_adapter()
    adapter.set_migration_mode("gradual")
    adapter.enable_compatibility_warnings(enable_warnings)
    adapter.enable_fallback(enable_fallback)
    
    print("已启用分布式日志系统迁移模式")
    print("- 迁移模式: 渐进式")
    print(f"- 兼容性警告: {'启用' if enable_warnings else '禁用'}")
    print(f"- 回退功能: {'启用' if enable_fallback else '禁用'}")


def complete_migration_to_distributed_logging():
    """
    完成迁移到分布式日志系统
    """
    adapter = get_compatibility_adapter()
    adapter.set_migration_mode("full")
    adapter.enable_compatibility_warnings(False)
    adapter.enable_fallback(False)
    
    print("已完成分布式日志系统迁移")
    print("- 迁移模式: 完全")
    print("- 只使用新的分布式日志系统")


def rollback_to_original_logging():
    """
    回滚到原有日志系统
    """
    adapter = get_compatibility_adapter()
    adapter.set_migration_mode("disabled")
    
    print("已回滚到原有日志系统")
    print("- 迁移模式: 禁用")
    print("- 只使用原有日志系统")


def get_migration_status() -> Dict[str, Any]:
    """
    获取迁移状态
    
    Returns:
        Dict[str, Any]: 迁移状态信息
    """
    adapter = get_compatibility_adapter()
    return adapter.get_migration_stats()


def test_logging_compatibility() -> Dict[str, Any]:
    """
    测试日志系统兼容性
    
    Returns:
        Dict[str, Any]: 测试结果
    """
    adapter = get_compatibility_adapter()
    return adapter.test_compatibility()
