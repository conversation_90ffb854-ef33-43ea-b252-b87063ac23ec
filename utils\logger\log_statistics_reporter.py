#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志统计报告器模块

生成详细的日志质量报告和统计信息
复用现有的分析引擎和去重器功能
"""

import os
import sys
import time
import json
from typing import Dict, List, Optional, Any
from collections import defaultdict
from dataclasses import dataclass, asdict
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的模块
from utils.logger.log_analysis_engine import get_log_analysis_engine, AnalysisResult
from utils.logger.log_deduplicator import get_log_deduplicator
from utils.logger.log_aggregator import get_log_aggregator
from utils.logger.intelligent_log_router import get_intelligent_log_router
from utils.logger.process_detector import get_process_detector


@dataclass
class LogQualityMetrics:
    """日志质量指标数据类"""
    total_logs: int
    unique_logs: int
    duplicate_logs: int
    duplicate_rate: float
    error_rate: float
    warning_rate: float
    info_rate: float
    debug_rate: float
    average_message_length: float
    quality_score: float


@dataclass
class ProcessMetrics:
    """进程指标数据类"""
    process_type: str
    log_count: int
    error_count: int
    warning_count: int
    duplicate_count: int
    efficiency_score: float


@dataclass
class SystemHealthReport:
    """系统健康报告数据类"""
    timestamp: float
    overall_health_score: float
    quality_metrics: LogQualityMetrics
    process_metrics: List[ProcessMetrics]
    top_issues: List[str]
    recommendations: List[str]
    trend_analysis: Dict[str, Any]


class LogStatisticsReporter:
    """
    日志统计报告器
    
    生成详细的日志质量报告和统计信息
    """
    
    def __init__(self, report_interval: float = 3600.0):
        """
        初始化日志统计报告器
        
        Args:
            report_interval: 报告生成间隔（秒）
        """
        self.report_interval = report_interval
        
        # 获取现有组件
        self.analysis_engine = get_log_analysis_engine()
        self.deduplicator = get_log_deduplicator()
        self.aggregator = get_log_aggregator()
        self.router = get_intelligent_log_router()
        self.process_detector = get_process_detector()
        
        # 历史数据
        self.historical_reports = []
        self.max_history = 24  # 保留24个报告（24小时）
        
        # 趋势数据
        self.trend_data = {
            'log_volume': [],
            'error_rate': [],
            'duplicate_rate': [],
            'quality_score': []
        }
    
    def generate_quality_report(self) -> SystemHealthReport:
        """
        生成系统健康报告
        
        Returns:
            SystemHealthReport: 系统健康报告
        """
        current_time = time.time()
        
        # 获取各组件统计
        analysis_stats = self.analysis_engine.get_analysis_stats()
        dedup_stats = self.deduplicator.get_stats()
        aggregator_stats = self.aggregator.get_stats()
        router_stats = self.router.get_routing_stats()
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(
            analysis_stats, dedup_stats, aggregator_stats
        )
        
        # 计算进程指标
        process_metrics = self._calculate_process_metrics(
            aggregator_stats, router_stats
        )
        
        # 识别主要问题
        top_issues = self._identify_top_issues(
            quality_metrics, process_metrics, analysis_stats
        )
        
        # 生成建议
        recommendations = self._generate_recommendations(
            quality_metrics, process_metrics, top_issues
        )
        
        # 计算整体健康评分
        overall_health_score = self._calculate_overall_health_score(
            quality_metrics, process_metrics
        )
        
        # 趋势分析
        trend_analysis = self._analyze_trends(quality_metrics)
        
        # 创建报告
        report = SystemHealthReport(
            timestamp=current_time,
            overall_health_score=overall_health_score,
            quality_metrics=quality_metrics,
            process_metrics=process_metrics,
            top_issues=top_issues,
            recommendations=recommendations,
            trend_analysis=trend_analysis
        )
        
        # 保存到历史记录
        self._save_to_history(report)
        
        return report
    
    def _calculate_quality_metrics(self, analysis_stats: Dict[str, Any], 
                                  dedup_stats: Dict[str, Any], 
                                  aggregator_stats: Dict[str, Any]) -> LogQualityMetrics:
        """
        计算日志质量指标
        
        Args:
            analysis_stats: 分析统计
            dedup_stats: 去重统计
            aggregator_stats: 聚合器统计
            
        Returns:
            LogQualityMetrics: 质量指标
        """
        total_logs = aggregator_stats.get('total_logs', 0)
        processed_logs = aggregator_stats.get('processed_logs', 0)
        
        # 去重统计
        duplicates_found = dedup_stats.get('duplicates_found', 0)
        unique_logs = total_logs - duplicates_found
        duplicate_rate = (duplicates_found / max(1, total_logs)) * 100
        
        # 级别分布
        by_level = aggregator_stats.get('by_level', {})
        error_count = by_level.get('ERROR', 0) + by_level.get('CRITICAL', 0)
        warning_count = by_level.get('WARNING', 0)
        info_count = by_level.get('INFO', 0)
        debug_count = by_level.get('DEBUG', 0)
        
        total_for_rates = max(1, error_count + warning_count + info_count + debug_count)
        error_rate = (error_count / total_for_rates) * 100
        warning_rate = (warning_count / total_for_rates) * 100
        info_rate = (info_count / total_for_rates) * 100
        debug_rate = (debug_count / total_for_rates) * 100
        
        # 平均消息长度（估算）
        average_message_length = 50.0  # 默认值，实际可以从日志中计算
        
        # 质量评分计算
        quality_score = self._calculate_quality_score(
            duplicate_rate, error_rate, warning_rate
        )
        
        return LogQualityMetrics(
            total_logs=total_logs,
            unique_logs=unique_logs,
            duplicate_logs=duplicates_found,
            duplicate_rate=duplicate_rate,
            error_rate=error_rate,
            warning_rate=warning_rate,
            info_rate=info_rate,
            debug_rate=debug_rate,
            average_message_length=average_message_length,
            quality_score=quality_score
        )
    
    def _calculate_process_metrics(self, aggregator_stats: Dict[str, Any], 
                                  router_stats: Dict[str, Any]) -> List[ProcessMetrics]:
        """
        计算进程指标
        
        Args:
            aggregator_stats: 聚合器统计
            router_stats: 路由器统计
            
        Returns:
            List[ProcessMetrics]: 进程指标列表
        """
        process_metrics = []
        
        by_process_type = aggregator_stats.get('by_process_type', {})
        routing_stats = router_stats.get('routing', {})
        
        for process_type, log_count in by_process_type.items():
            # 估算错误和警告数量（基于全局比例）
            total_logs = aggregator_stats.get('total_logs', 1)
            by_level = aggregator_stats.get('by_level', {})
            
            error_ratio = (by_level.get('ERROR', 0) + by_level.get('CRITICAL', 0)) / total_logs
            warning_ratio = by_level.get('WARNING', 0) / total_logs
            
            error_count = int(log_count * error_ratio)
            warning_count = int(log_count * warning_ratio)
            
            # 估算重复数量
            duplicate_count = 0  # 需要从去重器获取更详细的统计
            
            # 计算效率评分
            efficiency_score = self._calculate_process_efficiency_score(
                process_type, log_count, error_count, duplicate_count
            )
            
            process_metrics.append(ProcessMetrics(
                process_type=process_type,
                log_count=log_count,
                error_count=error_count,
                warning_count=warning_count,
                duplicate_count=duplicate_count,
                efficiency_score=efficiency_score
            ))
        
        return process_metrics
    
    def _calculate_quality_score(self, duplicate_rate: float, 
                                error_rate: float, warning_rate: float) -> float:
        """
        计算质量评分
        
        Args:
            duplicate_rate: 重复率
            error_rate: 错误率
            warning_rate: 警告率
            
        Returns:
            float: 质量评分 (0-100)
        """
        score = 100.0
        
        # 重复率惩罚
        if duplicate_rate > 50:
            score -= 30
        elif duplicate_rate > 20:
            score -= 15
        elif duplicate_rate > 10:
            score -= 5
        
        # 错误率惩罚
        if error_rate > 10:
            score -= 25
        elif error_rate > 5:
            score -= 15
        elif error_rate > 1:
            score -= 5
        
        # 警告率惩罚
        if warning_rate > 20:
            score -= 10
        elif warning_rate > 10:
            score -= 5
        
        return max(0.0, score)
    
    def _calculate_process_efficiency_score(self, process_type: str, 
                                          log_count: int, error_count: int, 
                                          duplicate_count: int) -> float:
        """
        计算进程效率评分
        
        Args:
            process_type: 进程类型
            log_count: 日志数量
            error_count: 错误数量
            duplicate_count: 重复数量
            
        Returns:
            float: 效率评分 (0-100)
        """
        if log_count == 0:
            return 100.0
        
        score = 100.0
        
        # 错误率惩罚
        error_rate = (error_count / log_count) * 100
        if error_rate > 5:
            score -= 30
        elif error_rate > 1:
            score -= 15
        
        # 重复率惩罚
        duplicate_rate = (duplicate_count / log_count) * 100
        if duplicate_rate > 30:
            score -= 20
        elif duplicate_rate > 10:
            score -= 10
        
        # 进程类型特定调整
        if process_type == 'worker':
            # 工作进程应该产生较少的日志
            if log_count > 1000:
                score -= 10
        elif process_type == 'main':
            # 主进程可以产生更多日志
            pass
        
        return max(0.0, score)
    
    def _identify_top_issues(self, quality_metrics: LogQualityMetrics, 
                           process_metrics: List[ProcessMetrics], 
                           analysis_stats: Dict[str, Any]) -> List[str]:
        """
        识别主要问题
        
        Args:
            quality_metrics: 质量指标
            process_metrics: 进程指标
            analysis_stats: 分析统计
            
        Returns:
            List[str]: 问题列表
        """
        issues = []
        
        # 质量问题
        if quality_metrics.duplicate_rate > 30:
            issues.append(f"重复日志率过高: {quality_metrics.duplicate_rate:.1f}%")
        
        if quality_metrics.error_rate > 5:
            issues.append(f"错误率过高: {quality_metrics.error_rate:.1f}%")
        
        if quality_metrics.quality_score < 70:
            issues.append(f"整体日志质量较低: {quality_metrics.quality_score:.1f}/100")
        
        # 进程问题
        for metrics in process_metrics:
            if metrics.efficiency_score < 60:
                issues.append(f"{metrics.process_type}进程效率低: {metrics.efficiency_score:.1f}/100")
        
        # 分析问题
        patterns_matched = analysis_stats.get('patterns_matched', 0)
        total_analyzed = analysis_stats.get('total_analyzed', 1)
        pattern_rate = (patterns_matched / total_analyzed) * 100
        
        if pattern_rate > 20:
            issues.append(f"问题模式匹配率高: {pattern_rate:.1f}%")
        
        return issues[:5]  # 返回前5个问题
    
    def _generate_recommendations(self, quality_metrics: LogQualityMetrics, 
                                 process_metrics: List[ProcessMetrics], 
                                 top_issues: List[str]) -> List[str]:
        """
        生成建议
        
        Args:
            quality_metrics: 质量指标
            process_metrics: 进程指标
            top_issues: 主要问题
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        # 基于质量指标的建议
        if quality_metrics.duplicate_rate > 20:
            recommendations.append("启用分布式日志系统的去重功能")
            recommendations.append("调整工作进程的日志级别为WARNING或更高")
        
        if quality_metrics.error_rate > 3:
            recommendations.append("检查系统错误，提高系统稳定性")
            recommendations.append("增加错误处理和异常捕获")
        
        # 基于进程指标的建议
        worker_metrics = [m for m in process_metrics if m.process_type == 'worker']
        if worker_metrics and worker_metrics[0].log_count > 1000:
            recommendations.append("减少工作进程的日志输出量")
        
        # 基于问题的建议
        if any("重复日志" in issue for issue in top_issues):
            recommendations.append("实施智能日志过滤策略")
        
        if any("错误率" in issue for issue in top_issues):
            recommendations.append("加强系统监控和预警机制")
        
        return recommendations[:5]  # 返回前5个建议
    
    def _calculate_overall_health_score(self, quality_metrics: LogQualityMetrics, 
                                       process_metrics: List[ProcessMetrics]) -> float:
        """
        计算整体健康评分
        
        Args:
            quality_metrics: 质量指标
            process_metrics: 进程指标
            
        Returns:
            float: 整体健康评分 (0-100)
        """
        # 质量评分权重60%
        quality_weight = 0.6
        quality_score = quality_metrics.quality_score
        
        # 进程效率评分权重40%
        process_weight = 0.4
        if process_metrics:
            avg_process_score = sum(m.efficiency_score for m in process_metrics) / len(process_metrics)
        else:
            avg_process_score = 100.0
        
        overall_score = quality_score * quality_weight + avg_process_score * process_weight
        
        return round(overall_score, 1)
    
    def _analyze_trends(self, quality_metrics: LogQualityMetrics) -> Dict[str, Any]:
        """
        分析趋势
        
        Args:
            quality_metrics: 质量指标
            
        Returns:
            Dict[str, Any]: 趋势分析结果
        """
        # 添加当前数据到趋势
        self.trend_data['log_volume'].append(quality_metrics.total_logs)
        self.trend_data['error_rate'].append(quality_metrics.error_rate)
        self.trend_data['duplicate_rate'].append(quality_metrics.duplicate_rate)
        self.trend_data['quality_score'].append(quality_metrics.quality_score)
        
        # 保持最近24个数据点
        for key in self.trend_data:
            if len(self.trend_data[key]) > 24:
                self.trend_data[key] = self.trend_data[key][-24:]
        
        # 计算趋势
        trends = {}
        for metric, values in self.trend_data.items():
            if len(values) >= 2:
                recent_avg = sum(values[-3:]) / len(values[-3:])
                older_avg = sum(values[:-3]) / max(1, len(values[:-3]))
                
                if older_avg > 0:
                    change_percent = ((recent_avg - older_avg) / older_avg) * 100
                    trends[metric] = {
                        'current': recent_avg,
                        'change_percent': round(change_percent, 1),
                        'trend': 'improving' if change_percent < 0 and metric in ['error_rate', 'duplicate_rate'] 
                                else 'improving' if change_percent > 0 and metric in ['quality_score', 'log_volume']
                                else 'declining' if change_percent != 0 else 'stable'
                    }
                else:
                    trends[metric] = {'current': recent_avg, 'change_percent': 0, 'trend': 'stable'}
            else:
                trends[metric] = {'current': values[-1] if values else 0, 'change_percent': 0, 'trend': 'insufficient_data'}
        
        return trends
    
    def _save_to_history(self, report: SystemHealthReport):
        """
        保存报告到历史记录
        
        Args:
            report: 系统健康报告
        """
        self.historical_reports.append(report)
        
        # 保持最大历史记录数
        if len(self.historical_reports) > self.max_history:
            self.historical_reports = self.historical_reports[-self.max_history:]
    
    def export_report_to_json(self, report: SystemHealthReport) -> str:
        """
        导出报告为JSON格式
        
        Args:
            report: 系统健康报告
            
        Returns:
            str: JSON格式的报告
        """
        # 转换为字典
        report_dict = asdict(report)
        
        # 格式化时间戳
        report_dict['timestamp_formatted'] = time.strftime(
            '%Y-%m-%d %H:%M:%S', time.localtime(report.timestamp)
        )
        
        return json.dumps(report_dict, indent=2, ensure_ascii=False)
    
    def get_historical_summary(self) -> Dict[str, Any]:
        """
        获取历史摘要
        
        Returns:
            Dict[str, Any]: 历史摘要
        """
        if not self.historical_reports:
            return {"message": "没有历史报告数据"}
        
        # 计算平均值
        avg_health_score = sum(r.overall_health_score for r in self.historical_reports) / len(self.historical_reports)
        avg_quality_score = sum(r.quality_metrics.quality_score for r in self.historical_reports) / len(self.historical_reports)
        
        # 最新报告
        latest_report = self.historical_reports[-1]
        
        return {
            "reports_count": len(self.historical_reports),
            "average_health_score": round(avg_health_score, 1),
            "average_quality_score": round(avg_quality_score, 1),
            "latest_health_score": latest_report.overall_health_score,
            "latest_quality_score": latest_report.quality_metrics.quality_score,
            "trend_summary": latest_report.trend_analysis
        }


# 全局统计报告器实例
_statistics_reporter_instance = None


def get_log_statistics_reporter() -> LogStatisticsReporter:
    """
    获取全局日志统计报告器实例
    
    Returns:
        LogStatisticsReporter: 统计报告器实例
    """
    global _statistics_reporter_instance
    if _statistics_reporter_instance is None:
        _statistics_reporter_instance = LogStatisticsReporter()
    return _statistics_reporter_instance
