#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
动态配置加载器模块

复用现有的环境变量机制，提供动态的日志配置加载功能
支持基于进程类型的配置继承和覆盖
"""

import os
import sys
import logging
from typing import Dict, Any, Optional, Union
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的配置模块
from config.settings import (
    ENABLE_DISTRIBUTED_LOGGING,
    ENABLE_LOG_DEDUPLICATION,
    ENABLE_PROCESS_AWARE_LOGGING,
    PROCESS_LOG_LEVELS,
    LOG_AGGREGATOR_CONFIG,
    LOG_DEDUPLICATOR_CONFIG,
    WORKER_PROCESS_LOG_SUPPRESSION,
    LOG_LEVEL,
    LOG_DIR
)
from utils.logger.process_detector import get_process_detector, ProcessType


class DynamicConfigLoader:
    """
    动态配置加载器
    
    复用现有的环境变量机制，提供动态的日志配置加载功能
    支持基于进程类型的配置继承和覆盖
    """
    
    def __init__(self):
        """初始化动态配置加载器"""
        self.process_detector = get_process_detector()
        self._config_cache = {}
        self._env_prefix = "QUANT_LOG_"
        
        # 默认配置模板
        self.default_config = {
            "distributed_logging": {
                "enabled": True,
                "enable_deduplication": True,
                "enable_process_awareness": True,
                "enable_aggregation": True
            },
            "log_levels": {
                "main": "DEBUG",
                "worker": "WARNING", 
                "child": "INFO",
                "unknown": "INFO"
            },
            "aggregator": {
                "max_queue_size": 10000,
                "batch_size": 100,
                "enable_stats": True
            },
            "deduplicator": {
                "cache_size": 10000,
                "time_window": 60.0,
                "similarity_threshold": 0.8
            },
            "suppression": {
                "suppress_init_logs": True,
                "suppress_debug_logs": True,
                "max_init_logs_per_module": 1
            }
        }
    
    def load_config(self, process_type: Optional[ProcessType] = None) -> Dict[str, Any]:
        """
        加载配置
        
        Args:
            process_type: 进程类型，如果为None则自动检测
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        if process_type is None:
            process_type = self.process_detector.detect_process_type()
        
        # 检查缓存
        cache_key = f"config_{process_type.value}"
        if cache_key in self._config_cache:
            return self._config_cache[cache_key]
        
        # 构建配置
        config = self._build_config(process_type)
        
        # 缓存配置
        self._config_cache[cache_key] = config
        
        return config
    
    def _build_config(self, process_type: ProcessType) -> Dict[str, Any]:
        """
        构建配置
        
        Args:
            process_type: 进程类型
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        # 从默认配置开始
        config = self._deep_copy_dict(self.default_config)
        
        # 应用全局配置
        self._apply_global_config(config)
        
        # 应用进程特定配置
        self._apply_process_specific_config(config, process_type)
        
        # 应用环境变量覆盖
        self._apply_env_overrides(config, process_type)
        
        return config
    
    def _apply_global_config(self, config: Dict[str, Any]):
        """
        应用全局配置
        
        Args:
            config: 配置字典
        """
        # 分布式日志配置
        config["distributed_logging"]["enabled"] = ENABLE_DISTRIBUTED_LOGGING
        config["distributed_logging"]["enable_deduplication"] = ENABLE_LOG_DEDUPLICATION
        config["distributed_logging"]["enable_process_awareness"] = ENABLE_PROCESS_AWARE_LOGGING
        
        # 日志级别配置
        config["log_levels"].update(PROCESS_LOG_LEVELS)
        
        # 聚合器配置
        config["aggregator"].update(LOG_AGGREGATOR_CONFIG)
        
        # 去重器配置
        config["deduplicator"].update(LOG_DEDUPLICATOR_CONFIG)
        
        # 抑制配置
        config["suppression"].update(WORKER_PROCESS_LOG_SUPPRESSION)
    
    def _apply_process_specific_config(self, config: Dict[str, Any], process_type: ProcessType):
        """
        应用进程特定配置
        
        Args:
            config: 配置字典
            process_type: 进程类型
        """
        process_type_str = process_type.value
        
        # 设置当前进程的日志级别
        if process_type_str in config["log_levels"]:
            config["current_log_level"] = config["log_levels"][process_type_str]
        else:
            config["current_log_level"] = config["log_levels"]["unknown"]
        
        # 工作进程特殊配置
        if process_type == ProcessType.WORKER:
            # 启用更严格的日志抑制
            config["suppression"]["suppress_init_logs"] = True
            config["suppression"]["suppress_debug_logs"] = True
            
            # 减少缓存大小以节省内存
            config["deduplicator"]["cache_size"] = min(
                config["deduplicator"]["cache_size"], 5000
            )
            
            # 禁用统计功能以提高性能
            config["aggregator"]["enable_stats"] = False
        
        # 主进程特殊配置
        elif process_type == ProcessType.MAIN:
            # 启用完整的日志功能
            config["suppression"]["suppress_init_logs"] = False
            config["suppression"]["suppress_debug_logs"] = False
            
            # 启用统计功能
            config["aggregator"]["enable_stats"] = True
    
    def _apply_env_overrides(self, config: Dict[str, Any], process_type: ProcessType):
        """
        应用环境变量覆盖
        
        Args:
            config: 配置字典
            process_type: 进程类型
        """
        process_type_str = process_type.value.upper()
        
        # 进程特定的环境变量覆盖
        env_overrides = {
            f"{self._env_prefix}{process_type_str}_LOG_LEVEL": ["current_log_level"],
            f"{self._env_prefix}{process_type_str}_SUPPRESS_INIT": ["suppression", "suppress_init_logs"],
            f"{self._env_prefix}{process_type_str}_SUPPRESS_DEBUG": ["suppression", "suppress_debug_logs"],
            f"{self._env_prefix}{process_type_str}_CACHE_SIZE": ["deduplicator", "cache_size"],
            f"{self._env_prefix}{process_type_str}_BATCH_SIZE": ["aggregator", "batch_size"]
        }
        
        for env_var, config_path in env_overrides.items():
            env_value = os.environ.get(env_var)
            if env_value is not None:
                self._set_nested_config(config, config_path, self._parse_env_value(env_value))
        
        # 通用环境变量覆盖
        common_overrides = {
            f"{self._env_prefix}ENABLE_DEDUP": ["distributed_logging", "enable_deduplication"],
            f"{self._env_prefix}ENABLE_AGGREGATION": ["distributed_logging", "enable_aggregation"],
            f"{self._env_prefix}TIME_WINDOW": ["deduplicator", "time_window"],
            f"{self._env_prefix}SIMILARITY_THRESHOLD": ["deduplicator", "similarity_threshold"]
        }
        
        for env_var, config_path in common_overrides.items():
            env_value = os.environ.get(env_var)
            if env_value is not None:
                self._set_nested_config(config, config_path, self._parse_env_value(env_value))
    
    def _set_nested_config(self, config: Dict[str, Any], path: list, value: Any):
        """
        设置嵌套配置值
        
        Args:
            config: 配置字典
            path: 配置路径列表
            value: 配置值
        """
        current = config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value
    
    def _parse_env_value(self, value: str) -> Union[str, int, float, bool]:
        """
        解析环境变量值
        
        Args:
            value: 环境变量值
            
        Returns:
            Union[str, int, float, bool]: 解析后的值
        """
        # 布尔值
        if value.lower() in ("true", "false"):
            return value.lower() == "true"
        
        # 整数
        try:
            return int(value)
        except ValueError:
            pass
        
        # 浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, ValueError):
            pass
        
        # 字符串
        return value
    
    def _deep_copy_dict(self, d: Dict[str, Any]) -> Dict[str, Any]:
        """
        深拷贝字典
        
        Args:
            d: 源字典
            
        Returns:
            Dict[str, Any]: 拷贝后的字典
        """
        result = {}
        for key, value in d.items():
            if isinstance(value, dict):
                result[key] = self._deep_copy_dict(value)
            elif isinstance(value, list):
                result[key] = value.copy()
            else:
                result[key] = value
        return result
    
    def get_log_level_for_process(self, process_type: ProcessType) -> str:
        """
        获取指定进程类型的日志级别
        
        Args:
            process_type: 进程类型
            
        Returns:
            str: 日志级别
        """
        config = self.load_config(process_type)
        return config.get("current_log_level", "INFO")
    
    def should_suppress_logs(self, process_type: ProcessType, log_type: str = "init") -> bool:
        """
        检查是否应该抑制指定类型的日志
        
        Args:
            process_type: 进程类型
            log_type: 日志类型 ("init", "debug")
            
        Returns:
            bool: 如果应该抑制返回True
        """
        config = self.load_config(process_type)
        suppression_config = config.get("suppression", {})
        
        if log_type == "init":
            return suppression_config.get("suppress_init_logs", False)
        elif log_type == "debug":
            return suppression_config.get("suppress_debug_logs", False)
        
        return False
    
    def clear_cache(self):
        """清空配置缓存"""
        self._config_cache.clear()
    
    def reload_config(self, process_type: Optional[ProcessType] = None) -> Dict[str, Any]:
        """
        重新加载配置
        
        Args:
            process_type: 进程类型，如果为None则自动检测
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        if process_type is None:
            process_type = self.process_detector.detect_process_type()
        
        # 清除缓存
        cache_key = f"config_{process_type.value}"
        if cache_key in self._config_cache:
            del self._config_cache[cache_key]
        
        # 重新加载
        return self.load_config(process_type)


# 全局配置加载器实例
_config_loader_instance = None


def get_dynamic_config_loader() -> DynamicConfigLoader:
    """
    获取全局动态配置加载器实例
    
    Returns:
        DynamicConfigLoader: 配置加载器实例
    """
    global _config_loader_instance
    if _config_loader_instance is None:
        _config_loader_instance = DynamicConfigLoader()
    return _config_loader_instance


def load_process_config(process_type: Optional[ProcessType] = None) -> Dict[str, Any]:
    """
    便利函数：加载进程配置
    
    Args:
        process_type: 进程类型，如果为None则自动检测
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    return get_dynamic_config_loader().load_config(process_type)


def get_process_log_level(process_type: Optional[ProcessType] = None) -> str:
    """
    便利函数：获取进程日志级别
    
    Args:
        process_type: 进程类型，如果为None则自动检测
        
    Returns:
        str: 日志级别
    """
    if process_type is None:
        process_type = get_process_detector().detect_process_type()
    
    return get_dynamic_config_loader().get_log_level_for_process(process_type)
