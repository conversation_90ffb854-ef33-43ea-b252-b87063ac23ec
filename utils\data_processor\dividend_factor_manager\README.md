# 复权因子管理器模块

## 概述

复权因子管理器模块提供统一的复权因子获取接口，集成本地数据覆盖范围检查和智能更新策略，为其他模块提供简单易用的复权因子获取服务。

## 主要特性

- **统一接口**: 提供简单的`get_dividend_factors()`接口，隐藏内部复杂性
- **智能更新**: 根据本地数据覆盖情况，智能选择最优更新策略
- **覆盖检查**: 在更新前检查本地数据是否已覆盖指定时间范围
- **错误处理**: 完善的错误处理和降级策略
- **批量处理**: 支持批量获取多只股票的复权因子数据

## 模块架构

```
dividend_factor_manager/
├── __init__.py                    # 模块初始化，导出主要接口
├── dividend_factor_manager.py    # 复权因子管理器（主要接口）
├── coverage_checker.py           # 覆盖范围检查器
├── smart_updater.py              # 智能更新器
└── README.md                     # 本文档
```

## 快速开始

### 基本使用

```python
from utils.data_processor.dividend_factor_manager import dividend_factor_manager

# 获取复权因子数据（自动检查覆盖范围和智能更新）
factors = dividend_factor_manager.get_dividend_factors(
    symbol="000001.SZ",
    start_date="20240101",
    end_date="20241231"
)

if factors is not None:
    print(f"获取到 {len(factors)} 条复权因子记录")
else:
    print("未获取到复权因子数据")
```

### 检查数据可用性

```python
# 检查数据可用性（不执行更新）
availability = dividend_factor_manager.check_data_availability(
    symbol="000001.SZ",
    start_date="20240101",
    end_date="20241231"
)

print(f"覆盖状态: {availability['coverage']['status']}")
print(f"推荐策略: {availability['recommended_strategy']}")
print(f"需要更新: {availability['needs_update']}")
```

### 手动更新

```python
# 手动更新复权因子数据
update_result = dividend_factor_manager.update_dividend_factors(
    symbol="000001.SZ",
    start_date="20240101",
    end_date="20241231",
    force_update=False
)

print(f"更新成功: {update_result['success']}")
print(f"更新策略: {update_result['strategy']}")
print(f"更新信息: {update_result['message']}")
```

### 批量处理

```python
# 批量获取多只股票的复权因子
symbols = ["000001.SZ", "600000.SH", "000002.SZ"]
batch_results = dividend_factor_manager.batch_get_dividend_factors(
    symbols=symbols,
    start_date="20240101",
    end_date="20241231"
)

for symbol, factors in batch_results.items():
    if factors is not None:
        print(f"{symbol}: {len(factors)} 条记录")
    else:
        print(f"{symbol}: 获取失败")
```

## 核心组件

### 1. DividendFactorManager

主要接口类，提供统一的复权因子获取服务。

**主要方法:**
- `get_dividend_factors()`: 获取复权因子数据（主要接口）
- `check_data_availability()`: 检查数据可用性
- `update_dividend_factors()`: 更新复权因子数据
- `batch_get_dividend_factors()`: 批量获取复权因子数据

### 2. CoverageChecker

覆盖范围检查器，负责检查本地数据是否覆盖指定时间范围。

**主要方法:**
- `check_coverage()`: 检查覆盖范围
- `get_missing_ranges()`: 获取缺失的时间范围
- `is_fully_covered()`: 检查是否完全覆盖

### 3. SmartUpdater

智能更新器，根据覆盖范围检查结果选择最优更新策略。

**更新策略:**
- `no_update`: 本地数据已完全覆盖，无需更新
- `full_update`: 无本地数据，执行完全更新
- `partial_update`: 部分覆盖，只更新缺失范围
- `force_update`: 强制更新所有数据

## 与现有模块的关系

### 与 dividend_factor_storage 的关系

本模块基于现有的`dividend_factor_storage`构建，作为其上层封装：

- **dividend_factor_storage**: 底层存储接口，负责数据的获取、存储、查询
- **dividend_factor_manager**: 上层管理接口，提供智能更新和统一访问

### 替代原有调用方式

**原有方式（复杂）:**
```python
# 需要手动处理复杂逻辑
factors = dividend_factor_storage.query_dividend_factors(symbol, start_date, end_date)
if factors is None or factors.empty:
    success = dividend_factor_storage.update_dividend_factors(symbol, False, start_date, end_date)
    if success:
        factors = dividend_factor_storage.query_dividend_factors(symbol, start_date, end_date)
```

**新方式（简单）:**
```python
# 一行代码搞定
factors = dividend_factor_manager.get_dividend_factors(symbol, start_date, end_date)
```

## 最佳实践

### 1. 自动更新 vs 手动更新

- **自动更新**: 适用于大多数场景，让管理器自动处理更新逻辑
- **手动更新**: 适用于需要精确控制更新时机的场景

### 2. 错误处理

```python
try:
    factors = dividend_factor_manager.get_dividend_factors(symbol, start_date, end_date)
    if factors is None:
        print("未获取到数据，可能是网络问题或数据源问题")
    else:
        # 处理数据
        pass
except Exception as e:
    print(f"获取复权因子失败: {e}")
```

### 3. 性能优化

- 对于批量处理，使用`batch_get_dividend_factors()`而不是循环调用
- 定期检查数据可用性，避免不必要的更新
- 合理设置时间范围，避免获取过多不需要的数据

## 常见问题

### Q: 如何处理网络连接问题？

A: 管理器内置了错误处理和降级策略，网络问题时会返回本地已有数据。

### Q: 如何强制更新所有数据？

A: 使用`force_update=True`参数：
```python
factors = dividend_factor_manager.get_dividend_factors(
    symbol="000001.SZ",
    start_date="20240101",
    end_date="20241231",
    force_update=True
)
```

### Q: 如何查看详细的更新日志？

A: 管理器使用统一的日志系统，可以通过日志文件查看详细信息。
