#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志过滤器模块

提供各种日志过滤器，用于根据不同条件过滤日志记录
"""

import logging

# 导入LogTarget枚举
from utils.logger.config import LogTarget


class TargetFilter(logging.Filter):
    """
    根据目标类型过滤日志记录

    此过滤器根据日志记录中的target属性过滤日志，
    使日志只输出到指定的目标（文件、控制台或两者）
    
    支持分别控制标题和正文的输出位置：
    - 通过title_target属性控制标题的输出位置
    - 通过content_target属性控制正文的输出位置
    """

    def __init__(self, target_type: int, default_target: int = LogTarget.FILE):
        """
        初始化过滤器

        Args:
            target_type: 目标类型，可以是LogTarget.FILE或LogTarget.CONSOLE
            default_target: 默认目标类型，未指定target时使用，默认为LogTarget.FILE
        """
        super().__init__()
        self.target_type = target_type
        self.default_target = default_target
        
    def filter(self, record: logging.LogRecord) -> bool:
        """
        过滤日志记录

        Args:
            record: 日志记录对象

        Returns:
            bool: 如果日志应该被输出到当前目标，则返回True，否则返回False
        """
        # 添加当前处理器类型属性，供格式化器使用
        record.current_handler_type = self.target_type
        
        # 检查是否使用新的参数格式（title_target和content_target）
        has_title_target = hasattr(record, 'title_target')
        has_content_target = hasattr(record, 'content_target')
        
        # 如果同时使用了title_target和content_target
        if has_title_target or has_content_target:
            # 获取标题和正文的目标
            title_target = getattr(record, 'title_target', self.default_target)
            content_target = getattr(record, 'content_target', self.default_target)
            
            # 尝试将字符串转换为数字
            if isinstance(title_target, str):
                title_target = LogTarget.from_code(title_target)
            elif not isinstance(title_target, int) or title_target is None:
                title_target = self.default_target
                
            if isinstance(content_target, str):
                content_target = LogTarget.from_code(content_target)
            elif not isinstance(content_target, int) or content_target is None:
                content_target = self.default_target
            
            # 检查目标是否匹配
            if self.target_type == LogTarget.FILE:
                # 文件目标：标题或正文的目标必须是FILE或BOTH
                return (title_target in (LogTarget.FILE, LogTarget.BOTH) or 
                        content_target in (LogTarget.FILE, LogTarget.BOTH))
            elif self.target_type == LogTarget.CONSOLE:
                # 控制台目标：标题或正文的目标必须是CONSOLE或BOTH
                return (title_target in (LogTarget.CONSOLE, LogTarget.BOTH) or 
                        content_target in (LogTarget.CONSOLE, LogTarget.BOTH))
            else:
                # 未知目标类型，默认允许
                return True
        
        # 使用旧的参数格式（target）
        # 检查记录是否有target属性
        if not hasattr(record, 'target') or getattr(record, 'target') is None:
            # 如果没有指定target或target为None，使用默认目标
            return self.target_type == self.default_target
            
        # 获取记录的目标
        record_target = getattr(record, 'target', self.default_target)
        
        # 尝试将字符串转换为数字
        if isinstance(record_target, str):
            record_target = LogTarget.from_code(record_target)
        elif not isinstance(record_target, int) or record_target is None:
            # 如果不是整数也不是字符串，或者是None，使用默认目标
            return self.target_type == self.default_target
            
        # 检查目标是否匹配
        if self.target_type == LogTarget.FILE:
            # 文件目标：记录目标必须是FILE或BOTH
            return record_target in (LogTarget.FILE, LogTarget.BOTH)
        elif self.target_type == LogTarget.CONSOLE:
            # 控制台目标：记录目标必须是CONSOLE或BOTH
            return record_target in (LogTarget.CONSOLE, LogTarget.BOTH)
        else:
            # 未知目标类型，默认允许
            return True


class LevelFilter(logging.Filter):
    """
    级别过滤器
    
    根据日志记录的级别过滤日志，只允许指定级别及以上的日志通过
    """
    
    def __init__(self, min_level=logging.NOTSET, max_level=logging.CRITICAL):
        """
        初始化级别过滤器
        
        Args:
            min_level: 最小级别（含），低于此级别的日志将被过滤掉
            max_level: 最大级别（含），高于此级别的日志将被过滤掉
        """
        super().__init__()
        self.min_level = min_level
        self.max_level = max_level
        
    def filter(self, record):
        """
        过滤日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 如果日志记录的级别在指定范围内，则返回True，否则返回False
        """
        # 检查日志级别是否在指定范围内
        return self.min_level <= record.levelno <= self.max_level


class PrefixFilter(logging.Filter):
    """
    前缀过滤器
    
    根据日志记录的前缀过滤日志，只允许包含指定前缀的日志通过
    """
    
    def __init__(self, prefix=None):
        """
        初始化前缀过滤器
        
        Args:
            prefix: 要匹配的前缀，如果为None则不进行过滤
        """
        super().__init__()
        self.prefix = prefix
        
    def filter(self, record):
        """
        过滤日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 如果日志记录的消息包含指定前缀，则返回True，否则返回False
        """
        if self.prefix is None:
            return True
            
        # 获取日志消息
        message = record.getMessage()
        
        # 检查消息是否以指定前缀开头
        return message.startswith(self.prefix)


class ModuleFilter(logging.Filter):
    """
    模块过滤器
    
    根据日志记录的模块名称过滤日志，只允许来自指定模块的日志通过
    """
    
    def __init__(self, module_name=None):
        """
        初始化模块过滤器
        
        Args:
            module_name: 要匹配的模块名称，如果为None则不进行过滤
        """
        super().__init__()
        self.module_name = module_name
        
    def filter(self, record):
        """
        过滤日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 如果日志记录来自指定模块，则返回True，否则返回False
        """
        if self.module_name is None:
            return True
            
        # 获取日志记录的模块名称
        logger_name = record.name
        
        # 检查模块名称是否匹配
        # 支持前缀匹配，例如：module_name="data"会匹配"data.submodule"
        return (logger_name == self.module_name or
                logger_name.startswith(f"{self.module_name}."))


class ProcessTypeFilter(logging.Filter):
    """
    进程类型过滤器

    基于进程类型和角色过滤日志记录
    复用现有的进程检测机制
    """

    def __init__(self,
                 allowed_process_types: List[str] = None,
                 blocked_process_types: List[str] = None,
                 suppress_init_logs_for_workers: bool = True,
                 suppress_debug_logs_for_workers: bool = True):
        """
        初始化进程类型过滤器

        Args:
            allowed_process_types: 允许的进程类型列表
            blocked_process_types: 阻止的进程类型列表
            suppress_init_logs_for_workers: 是否抑制工作进程的初始化日志
            suppress_debug_logs_for_workers: 是否抑制工作进程的调试日志
        """
        super().__init__()
        self.allowed_process_types = set(allowed_process_types) if allowed_process_types else None
        self.blocked_process_types = set(blocked_process_types) if blocked_process_types else set()
        self.suppress_init_logs_for_workers = suppress_init_logs_for_workers
        self.suppress_debug_logs_for_workers = suppress_debug_logs_for_workers

        # 延迟导入避免循环依赖
        self._process_detector = None
        self._init_keywords = [
            '初始化完成', '初始化成功', '已注册', '已加载', '版本:', '提供',
            'initialization complete', 'successfully initialized', 'registered', 'loaded'
        ]

    def _get_process_detector(self):
        """获取进程检测器实例"""
        if self._process_detector is None:
            from utils.logger.process_detector import get_process_detector
            self._process_detector = get_process_detector()
        return self._process_detector

    def filter(self, record: logging.LogRecord) -> bool:
        """
        过滤日志记录

        Args:
            record: 日志记录对象

        Returns:
            bool: 如果允许记录返回True
        """
        try:
            detector = self._get_process_detector()
            process_type = detector.detect_process_type().value

            # 检查是否在阻止列表中
            if process_type in self.blocked_process_types:
                return False

            # 检查是否在允许列表中（如果设置了允许列表）
            if self.allowed_process_types is not None:
                if process_type not in self.allowed_process_types:
                    return False

            # 工作进程特殊过滤规则
            if process_type == 'worker':
                # 抑制初始化日志
                if self.suppress_init_logs_for_workers:
                    message = record.getMessage().lower()
                    if any(keyword in message for keyword in self._init_keywords):
                        return False

                # 抑制调试日志
                if self.suppress_debug_logs_for_workers:
                    if record.levelno < logging.INFO:
                        return False

            return True

        except Exception:
            # 如果检测失败，默认允许
            return True


class IntelligentLogFilter(logging.Filter):
    """
    智能日志过滤器

    结合多种过滤策略，提供智能的日志过滤功能
    """

    def __init__(self):
        """初始化智能日志过滤器"""
        super().__init__()

        # 子过滤器
        self.process_filter = ProcessTypeFilter()
        self.target_filter = None
        self.prefix_filter = None
        self.module_filter = None

        # 过滤统计
        self.stats = {
            'total_records': 0,
            'filtered_records': 0,
            'by_process_type': {},
            'by_level': {},
            'by_reason': {}
        }

        # 延迟导入避免循环依赖
        self._deduplicator = None

    def _get_deduplicator(self):
        """获取去重器实例"""
        if self._deduplicator is None:
            try:
                from utils.logger.log_deduplicator import get_log_deduplicator
                self._deduplicator = get_log_deduplicator()
            except ImportError:
                self._deduplicator = None
        return self._deduplicator

    def add_target_filter(self, target_type):
        """添加目标过滤器"""
        self.target_filter = TargetFilter(target_type)

    def add_prefix_filter(self, prefix=None):
        """添加前缀过滤器"""
        self.prefix_filter = PrefixFilter(prefix)

    def add_module_filter(self, module_name=None):
        """添加模块过滤器"""
        self.module_filter = ModuleFilter(module_name)

    def filter(self, record: logging.LogRecord) -> bool:
        """
        智能过滤日志记录

        Args:
            record: 日志记录对象

        Returns:
            bool: 如果允许记录返回True
        """
        self.stats['total_records'] += 1

        # 更新级别统计
        level_name = record.levelname
        self.stats['by_level'][level_name] = self.stats['by_level'].get(level_name, 0) + 1

        try:
            # 进程类型过滤
            if not self.process_filter.filter(record):
                self._update_filter_stats('process_type', record)
                return False

            # 目标过滤
            if self.target_filter and not self.target_filter.filter(record):
                self._update_filter_stats('target', record)
                return False

            # 前缀过滤
            if self.prefix_filter and not self.prefix_filter.filter(record):
                self._update_filter_stats('prefix', record)
                return False

            # 模块过滤
            if self.module_filter and not self.module_filter.filter(record):
                self._update_filter_stats('module', record)
                return False

            # 去重过滤
            deduplicator = self._get_deduplicator()
            if deduplicator:
                try:
                    from utils.logger.log_aggregator import LogEntry
                    log_entry = LogEntry(
                        timestamp=record.created,
                        level=record.levelno,
                        message=record.getMessage(),
                        module=record.name,
                        function=record.funcName,
                        line_number=record.lineno,
                        process_id=os.getpid(),
                        process_type=getattr(record, 'process_type', 'unknown'),
                        thread_id=record.thread,
                        extra_data=getattr(record, 'extra_data', None)
                    )

                    is_duplicate, reason = deduplicator.is_duplicate(log_entry)
                    if is_duplicate:
                        self._update_filter_stats('duplicate', record, reason)
                        return False
                except ImportError:
                    # 如果无法导入LogEntry，跳过去重
                    pass

            return True

        except Exception:
            # 过滤失败，默认允许
            return True

    def _update_filter_stats(self, reason: str, record: logging.LogRecord, detail: str = None):
        """
        更新过滤统计

        Args:
            reason: 过滤原因
            record: 日志记录对象
            detail: 详细信息
        """
        self.stats['filtered_records'] += 1

        # 更新原因统计
        full_reason = f"{reason}:{detail}" if detail else reason
        self.stats['by_reason'][full_reason] = self.stats['by_reason'].get(full_reason, 0) + 1

        # 更新进程类型统计
        process_type = getattr(record, 'process_type', 'unknown')
        if process_type not in self.stats['by_process_type']:
            self.stats['by_process_type'][process_type] = {'total': 0, 'filtered': 0}
        self.stats['by_process_type'][process_type]['filtered'] += 1

    def get_filter_stats(self) -> Dict[str, Any]:
        """
        获取过滤统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        total = max(1, self.stats['total_records'])
        return {
            'total_records': self.stats['total_records'],
            'filtered_records': self.stats['filtered_records'],
            'pass_through_records': total - self.stats['filtered_records'],
            'filter_rate': (self.stats['filtered_records'] / total) * 100,
            'by_process_type': self.stats['by_process_type'].copy(),
            'by_level': self.stats['by_level'].copy(),
            'by_reason': self.stats['by_reason'].copy()
        }

    def clear_stats(self):
        """清空统计信息"""
        self.stats = {
            'total_records': 0,
            'filtered_records': 0,
            'by_process_type': {},
            'by_level': {},
            'by_reason': {}
        }