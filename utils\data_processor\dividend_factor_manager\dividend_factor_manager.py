"""
复权因子管理器

该模块提供统一的复权因子获取接口，集成覆盖范围检查和智能更新策略，
为其他模块提供简单易用的复权因子获取服务。
"""

import pandas as pd
from typing import Optional, Dict, Any

from utils.logger import get_unified_logger, LogTarget
from utils.data_processor.adjustment.dividend_factor_storage import dividend_factor_storage
from .coverage_checker import CoverageChecker
from .smart_updater import SmartUpdater

logger = get_unified_logger(__name__)


class DividendFactorManager:
    """复权因子管理器
    
    提供统一的复权因子获取接口，集成覆盖范围检查和智能更新策略
    """
    
    def __init__(self):
        """初始化复权因子管理器"""
        self.factor_storage = dividend_factor_storage
        self.coverage_checker = CoverageChecker()
        self.smart_updater = SmartUpdater()
        logger.info(LogTarget.FILE, "复权因子管理器初始化完成")
    
    def get_dividend_factors(
        self, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        auto_update: bool = True,
        force_update: bool = False
    ) -> Optional[pd.DataFrame]:
        """获取复权因子数据（主要接口）
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            auto_update: 是否自动更新缺失数据
            force_update: 是否强制更新
            
        Returns:
            复权因子数据DataFrame，如果获取失败返回None
        """
        try:
            logger.info(LogTarget.FILE, f"获取股票 {symbol} 复权因子数据: {start_date} - {end_date}")
            
            # 如果需要自动更新，先执行智能更新
            if auto_update:
                update_result = self.smart_updater.smart_update(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    force_update=force_update
                )
                
                if not update_result['success'] and update_result['strategy'] != 'no_update':
                    logger.warning(LogTarget.FILE, f"股票 {symbol} 复权因子更新失败: {update_result['message']}")
                    # 即使更新失败，也尝试返回本地已有数据
                else:
                    logger.debug(LogTarget.FILE, f"股票 {symbol} 复权因子更新结果: {update_result['strategy']}")
            
            # 从本地获取复权因子数据
            dividend_factors = self.factor_storage.query_dividend_factors(
                stock_code=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if dividend_factors is None or dividend_factors.empty:
                logger.warning(LogTarget.FILE, f"股票 {symbol} 无可用的复权因子数据")
                return None
            
            logger.info(LogTarget.FILE, f"成功获取股票 {symbol} 复权因子数据，共 {len(dividend_factors)} 条记录")
            return dividend_factors
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"获取股票 {symbol} 复权因子数据失败: {e}")
            return None
    
    def check_data_availability(
        self, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """检查复权因子数据可用性
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 数据可用性信息
        """
        try:
            coverage_result = self.coverage_checker.check_coverage(symbol, start_date, end_date)
            update_strategy = self.smart_updater.get_update_strategy(symbol, start_date, end_date)
            
            return {
                'symbol': symbol,
                'coverage': coverage_result,
                'recommended_strategy': update_strategy,
                'needs_update': update_strategy != 'no_update'
            }
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"检查股票 {symbol} 复权因子数据可用性失败: {e}")
            return {
                'symbol': symbol,
                'coverage': {'status': 'error', 'error': str(e)},
                'recommended_strategy': 'error',
                'needs_update': True
            }
    
    def update_dividend_factors(
        self, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        force_update: bool = False
    ) -> Dict[str, Any]:
        """更新复权因子数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            force_update: 是否强制更新
            
        Returns:
            Dict: 更新结果
        """
        return self.smart_updater.smart_update(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            force_update=force_update
        )
    
    def batch_get_dividend_factors(
        self, 
        symbols: list, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        auto_update: bool = True
    ) -> Dict[str, Optional[pd.DataFrame]]:
        """批量获取复权因子数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            auto_update: 是否自动更新
            
        Returns:
            Dict: {symbol: dividend_factors} 字典
        """
        results = {}
        
        for symbol in symbols:
            try:
                factors = self.get_dividend_factors(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    auto_update=auto_update
                )
                results[symbol] = factors
                
            except Exception as e:
                logger.error(LogTarget.FILE, f"批量获取股票 {symbol} 复权因子失败: {e}")
                results[symbol] = None
        
        return results
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息
        
        Returns:
            Dict: 存储信息
        """
        try:
            return self.factor_storage.get_storage_info()
        except Exception as e:
            logger.error(LogTarget.FILE, f"获取复权因子存储信息失败: {e}")
            return {'error': str(e)}
    
    def clear_cache(self, symbol: Optional[str] = None) -> bool:
        """清理缓存
        
        Args:
            symbol: 股票代码，如果为None则清理所有缓存
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if symbol:
                logger.info(LogTarget.FILE, f"清理股票 {symbol} 复权因子缓存")
                # 这里可以添加具体的缓存清理逻辑
                return True
            else:
                logger.info(LogTarget.FILE, "清理所有复权因子缓存")
                # 这里可以添加具体的缓存清理逻辑
                return True
                
        except Exception as e:
            logger.error(LogTarget.FILE, f"清理复权因子缓存失败: {e}")
            return False
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码格式
        
        Args:
            symbol: 股票代码
            
        Returns:
            bool: 代码格式是否有效
        """
        try:
            # 基本格式验证：应该包含'.'分隔符
            if '.' not in symbol:
                return False
            
            parts = symbol.split('.')
            if len(parts) != 2:
                return False
            
            code, exchange = parts
            
            # 验证交易所代码
            valid_exchanges = ['SZ', 'SH', 'BJ']  # 深圳、上海、北京
            if exchange.upper() not in valid_exchanges:
                return False
            
            # 验证股票代码长度
            if len(code) != 6:
                return False
            
            # 验证股票代码是否为数字
            if not code.isdigit():
                return False
            
            return True
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"验证股票代码 {symbol} 失败: {e}")
            return False
