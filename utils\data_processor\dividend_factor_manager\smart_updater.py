"""
智能复权因子更新器

该模块根据覆盖范围检查结果，智能决策是否需要更新以及更新范围，
避免无效的API调用，提高更新效率。
"""

from typing import Optional, List, Tuple, Dict, Any
from datetime import datetime, timedelta

from utils.logger import get_unified_logger, LogTarget
from utils.smart_time_converter import smart_to_datetime
from utils.data_processor.adjustment.dividend_factor_storage import dividend_factor_storage
from .coverage_checker import CoverageChecker

logger = get_unified_logger(__name__)


class SmartUpdater:
    """智能复权因子更新器
    
    根据覆盖范围检查结果，智能选择最优的更新策略
    """
    
    def __init__(self):
        """初始化智能更新器"""
        self.factor_storage = dividend_factor_storage
        self.coverage_checker = CoverageChecker()
        logger.debug(LogTarget.FILE, "智能复权因子更新器初始化完成")
    
    def smart_update(
        self, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        force_update: bool = False
    ) -> Dict[str, Any]:
        """智能更新复权因子数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            force_update: 是否强制更新
            
        Returns:
            Dict: 更新结果
            {
                'success': bool,
                'strategy': 更新策略,
                'updated_ranges': 更新的时间范围,
                'message': 更新信息
            }
        """
        try:
            logger.info(LogTarget.FILE, f"开始智能更新股票 {symbol} 复权因子数据")
            
            if force_update:
                return self._force_update(symbol, start_date, end_date)
            
            # 检查覆盖范围
            coverage_result = self.coverage_checker.check_coverage(symbol, start_date, end_date)
            
            # 根据覆盖情况选择更新策略
            if coverage_result['status'] == 'full_coverage':
                return self._no_update_needed(symbol, coverage_result)
            elif coverage_result['status'] == 'no_coverage':
                return self._full_update(symbol, start_date, end_date)
            elif coverage_result['status'] == 'partial_coverage':
                return self._partial_update(symbol, coverage_result)
            else:
                return self._handle_unknown_status(symbol, coverage_result)
                
        except Exception as e:
            logger.error(LogTarget.FILE, f"智能更新股票 {symbol} 复权因子失败: {e}")
            return {
                'success': False,
                'strategy': 'error',
                'updated_ranges': [],
                'message': f"更新失败: {e}",
                'error': str(e)
            }
    
    def _no_update_needed(self, symbol: str, coverage_result: Dict) -> Dict[str, Any]:
        """无需更新策略"""
        logger.info(LogTarget.FILE, f"股票 {symbol} 本地复权因子数据已完全覆盖，无需更新")
        return {
            'success': True,
            'strategy': 'no_update',
            'updated_ranges': [],
            'message': '本地数据已完全覆盖指定时间范围，无需更新',
            'coverage_result': coverage_result
        }
    
    def _full_update(self, symbol: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """完全更新策略"""
        logger.info(LogTarget.FILE, f"股票 {symbol} 无本地复权因子数据，执行完全更新")
        
        # 验证时间范围
        if not self._validate_time_range(start_date, end_date):
            return {
                'success': False,
                'strategy': 'full_update',
                'updated_ranges': [],
                'message': '时间范围无效：开始时间不能晚于结束时间'
            }
        
        # 执行完全更新
        success = self.factor_storage.update_dividend_factors(
            stock_code=symbol,
            force_update=True,
            start_time=start_date or '',
            end_time=end_date or ''
        )
        
        return {
            'success': success,
            'strategy': 'full_update',
            'updated_ranges': [(start_date, end_date)] if start_date and end_date else [],
            'message': '完全更新成功' if success else '完全更新失败'
        }
    
    def _partial_update(self, symbol: str, coverage_result: Dict) -> Dict[str, Any]:
        """部分更新策略"""
        missing_ranges = coverage_result.get('missing_ranges', [])
        
        if not missing_ranges:
            return self._no_update_needed(symbol, coverage_result)
        
        logger.info(LogTarget.FILE, f"股票 {symbol} 需要部分更新，缺失范围: {missing_ranges}")
        
        updated_ranges = []
        all_success = True
        
        for start_date, end_date in missing_ranges:
            # 验证时间范围
            if not self._validate_time_range(start_date, end_date):
                logger.warning(LogTarget.FILE, f"跳过无效时间范围: {start_date} - {end_date}")
                all_success = False
                continue
            
            # 执行部分更新
            success = self.factor_storage.update_dividend_factors(
                stock_code=symbol,
                force_update=False,
                start_time=start_date,
                end_time=end_date
            )
            
            if success:
                updated_ranges.append((start_date, end_date))
                logger.info(LogTarget.FILE, f"股票 {symbol} 部分更新成功: {start_date} - {end_date}")
            else:
                logger.error(LogTarget.FILE, f"股票 {symbol} 部分更新失败: {start_date} - {end_date}")
                all_success = False
        
        return {
            'success': all_success,
            'strategy': 'partial_update',
            'updated_ranges': updated_ranges,
            'message': f'部分更新{"成功" if all_success else "部分失败"}，更新了 {len(updated_ranges)} 个时间范围'
        }
    
    def _force_update(self, symbol: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """强制更新策略"""
        logger.info(LogTarget.FILE, f"股票 {symbol} 执行强制更新")
        
        # 验证时间范围
        if not self._validate_time_range(start_date, end_date):
            return {
                'success': False,
                'strategy': 'force_update',
                'updated_ranges': [],
                'message': '时间范围无效：开始时间不能晚于结束时间'
            }
        
        # 执行强制更新
        success = self.factor_storage.update_dividend_factors(
            stock_code=symbol,
            force_update=True,
            start_time=start_date or '',
            end_time=end_date or ''
        )
        
        return {
            'success': success,
            'strategy': 'force_update',
            'updated_ranges': [(start_date, end_date)] if start_date and end_date else [],
            'message': '强制更新成功' if success else '强制更新失败'
        }
    
    def _handle_unknown_status(self, symbol: str, coverage_result: Dict) -> Dict[str, Any]:
        """处理未知状态"""
        logger.warning(LogTarget.FILE, f"股票 {symbol} 覆盖检查返回未知状态: {coverage_result}")
        return {
            'success': False,
            'strategy': 'unknown',
            'updated_ranges': [],
            'message': f"未知的覆盖状态: {coverage_result.get('status', 'unknown')}",
            'coverage_result': coverage_result
        }
    
    def _validate_time_range(self, start_date: Optional[str], end_date: Optional[str]) -> bool:
        """验证时间范围的有效性
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            bool: 时间范围是否有效
        """
        if not start_date or not end_date:
            return True  # 空时间范围认为有效
        
        try:
            start_dt = smart_to_datetime(start_date)
            end_dt = smart_to_datetime(end_date)
            
            if start_dt > end_dt:
                logger.error(LogTarget.FILE, f"时间范围无效: 开始时间 {start_date} 晚于结束时间 {end_date}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"时间范围验证失败: {e}")
            return False
    
    def get_update_strategy(
        self, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> str:
        """获取推荐的更新策略（不执行更新）
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            str: 推荐的更新策略
        """
        try:
            coverage_result = self.coverage_checker.check_coverage(symbol, start_date, end_date)
            
            if coverage_result['status'] == 'full_coverage':
                return 'no_update'
            elif coverage_result['status'] == 'no_coverage':
                return 'full_update'
            elif coverage_result['status'] == 'partial_coverage':
                return 'partial_update'
            else:
                return 'unknown'
                
        except Exception as e:
            logger.error(LogTarget.FILE, f"获取更新策略失败: {e}")
            return 'error'
