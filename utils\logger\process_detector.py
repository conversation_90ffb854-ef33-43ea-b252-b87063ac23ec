#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
进程类型检测器模块

提供进程类型识别和角色检测功能，用于分布式日志系统
复用项目现有的进程管理和配置机制
"""

import os
import sys
import multiprocessing
from typing import Optional, Dict, Any
from enum import Enum

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的日志和配置模块
from utils.logger.config import LogTarget
from config.settings import GLOBAL_POOL_MAX_WORKERS


class ProcessType(Enum):
    """进程类型枚举"""
    MAIN = "main"           # 主进程
    WORKER = "worker"       # 工作进程
    CHILD = "child"         # 子进程
    UNKNOWN = "unknown"     # 未知类型


class ProcessRole(Enum):
    """进程角色枚举"""
    COORDINATOR = "coordinator"     # 协调者（主进程）
    EXECUTOR = "executor"          # 执行者（工作进程）
    HELPER = "helper"              # 辅助进程
    STANDALONE = "standalone"      # 独立进程


class ProcessTypeDetector:
    """
    进程类型检测器
    
    基于现有的multiprocessing和os模块，检测当前进程的类型和角色
    复用项目现有的进程管理机制
    """
    
    _instance = None
    _detection_cache = {}
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化检测器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._current_pid = os.getpid()
        self._parent_pid = os.getppid()
        self._process_name = None
        self._is_main_process = None
        
        # 尝试获取当前进程信息
        try:
            current_process = multiprocessing.current_process()
            self._process_name = current_process.name
            self._is_main_process = (current_process.name == "MainProcess")
        except Exception:
            # 在某些情况下可能无法获取进程信息
            self._process_name = "Unknown"
            self._is_main_process = True  # 默认假设是主进程
    
    def detect_process_type(self) -> ProcessType:
        """
        检测当前进程类型
        
        Returns:
            ProcessType: 进程类型
        """
        # 使用缓存避免重复检测
        cache_key = f"type_{self._current_pid}"
        if cache_key in self._detection_cache:
            return self._detection_cache[cache_key]
        
        process_type = ProcessType.UNKNOWN
        
        try:
            # 检查是否是主进程
            if self._is_main_process:
                process_type = ProcessType.MAIN
            else:
                # 检查进程名称模式
                if self._process_name and "Process-" in self._process_name:
                    # multiprocessing.Pool创建的工作进程通常命名为Process-N
                    process_type = ProcessType.WORKER
                elif self._process_name and self._process_name != "MainProcess":
                    # 其他命名的子进程
                    process_type = ProcessType.CHILD
                else:
                    # 无法确定，检查父进程关系
                    if self._parent_pid != self._current_pid:
                        process_type = ProcessType.CHILD
                    else:
                        process_type = ProcessType.MAIN
        
        except Exception:
            # 检测失败，默认为未知类型
            process_type = ProcessType.UNKNOWN
        
        # 缓存结果
        self._detection_cache[cache_key] = process_type
        return process_type
    
    def detect_process_role(self) -> ProcessRole:
        """
        检测当前进程角色
        
        Returns:
            ProcessRole: 进程角色
        """
        # 使用缓存避免重复检测
        cache_key = f"role_{self._current_pid}"
        if cache_key in self._detection_cache:
            return self._detection_cache[cache_key]
        
        process_type = self.detect_process_type()
        process_role = ProcessRole.STANDALONE
        
        if process_type == ProcessType.MAIN:
            process_role = ProcessRole.COORDINATOR
        elif process_type == ProcessType.WORKER:
            process_role = ProcessRole.EXECUTOR
        elif process_type == ProcessType.CHILD:
            process_role = ProcessRole.HELPER
        else:
            process_role = ProcessRole.STANDALONE
        
        # 缓存结果
        self._detection_cache[cache_key] = process_role
        return process_role
    
    def is_main_process(self) -> bool:
        """
        检查是否是主进程
        
        Returns:
            bool: 如果是主进程返回True
        """
        return self.detect_process_type() == ProcessType.MAIN
    
    def is_worker_process(self) -> bool:
        """
        检查是否是工作进程
        
        Returns:
            bool: 如果是工作进程返回True
        """
        return self.detect_process_type() == ProcessType.WORKER
    
    def get_process_info(self) -> Dict[str, Any]:
        """
        获取当前进程的详细信息
        
        Returns:
            Dict[str, Any]: 进程信息字典
        """
        return {
            'pid': self._current_pid,
            'parent_pid': self._parent_pid,
            'process_name': self._process_name,
            'process_type': self.detect_process_type().value,
            'process_role': self.detect_process_role().value,
            'is_main_process': self.is_main_process(),
            'is_worker_process': self.is_worker_process()
        }
    
    def should_suppress_init_logs(self) -> bool:
        """
        判断是否应该抑制初始化日志
        
        Returns:
            bool: 如果应该抑制初始化日志返回True
        """
        # 工作进程应该抑制大部分初始化日志
        return self.is_worker_process()
    
    def get_recommended_log_level(self) -> str:
        """
        获取推荐的日志级别
        
        Returns:
            str: 推荐的日志级别
        """
        process_type = self.detect_process_type()
        
        if process_type == ProcessType.MAIN:
            return "DEBUG"  # 主进程记录详细日志
        elif process_type == ProcessType.WORKER:
            return "WARNING"  # 工作进程只记录警告及以上
        elif process_type == ProcessType.CHILD:
            return "INFO"  # 子进程记录信息及以上
        else:
            return "INFO"  # 默认信息级别
    
    @classmethod
    def get_instance(cls) -> 'ProcessTypeDetector':
        """
        获取检测器实例（单例模式）
        
        Returns:
            ProcessTypeDetector: 检测器实例
        """
        return cls()


# 全局实例
_detector_instance = None


def get_process_detector() -> ProcessTypeDetector:
    """
    获取全局进程检测器实例
    
    Returns:
        ProcessTypeDetector: 检测器实例
    """
    global _detector_instance
    if _detector_instance is None:
        _detector_instance = ProcessTypeDetector()
    return _detector_instance


def is_main_process() -> bool:
    """
    便利函数：检查是否是主进程
    
    Returns:
        bool: 如果是主进程返回True
    """
    return get_process_detector().is_main_process()


def is_worker_process() -> bool:
    """
    便利函数：检查是否是工作进程
    
    Returns:
        bool: 如果是工作进程返回True
    """
    return get_process_detector().is_worker_process()


def should_suppress_init_logs() -> bool:
    """
    便利函数：判断是否应该抑制初始化日志
    
    Returns:
        bool: 如果应该抑制初始化日志返回True
    """
    return get_process_detector().should_suppress_init_logs()


def get_recommended_log_level() -> str:
    """
    便利函数：获取推荐的日志级别

    Returns:
        str: 推荐的日志级别
    """
    return get_process_detector().get_recommended_log_level()


class ProcessRoleIdentifier:
    """
    进程角色识别器

    基于进程类型和运行环境，识别进程的具体角色和职责
    """

    def __init__(self, process_detector: ProcessTypeDetector):
        """
        初始化进程角色识别器

        Args:
            process_detector: 进程类型检测器实例
        """
        self.process_detector = process_detector
        self._role_cache = {}
        self._environment_info = self._detect_environment()

    def _detect_environment(self) -> Dict[str, Any]:
        """
        检测运行环境信息

        Returns:
            Dict[str, Any]: 环境信息字典
        """
        try:
            import psutil
            current_process = psutil.Process()
            parent_process = current_process.parent()

            return {
                "has_parent": parent_process is not None,
                "parent_name": parent_process.name() if parent_process else None,
                "parent_pid": parent_process.pid if parent_process else None,
                "cmdline": current_process.cmdline(),
                "cwd": current_process.cwd(),
                "num_threads": current_process.num_threads()
            }
        except (ImportError, Exception):
            # 如果psutil不可用或出现异常，使用基本信息
            return {
                "has_parent": os.getppid() != os.getpid(),
                "parent_name": None,
                "parent_pid": os.getppid(),
                "cmdline": [],
                "cwd": os.getcwd(),
                "num_threads": 1
            }

    def identify_role(self) -> ProcessRole:
        """
        识别当前进程的角色

        Returns:
            ProcessRole: 进程角色
        """
        # 使用缓存避免重复识别
        cache_key = f"role_{os.getpid()}"
        if cache_key in self._role_cache:
            return self._role_cache[cache_key]

        process_type = self.process_detector.detect_process_type()
        process_role = self._determine_role(process_type)

        # 缓存结果
        self._role_cache[cache_key] = process_role
        return process_role

    def _determine_role(self, process_type: ProcessType) -> ProcessRole:
        """
        根据进程类型和环境信息确定角色

        Args:
            process_type: 进程类型

        Returns:
            ProcessRole: 进程角色
        """
        if process_type == ProcessType.MAIN:
            return ProcessRole.COORDINATOR
        elif process_type == ProcessType.WORKER:
            # 进一步区分工作进程的具体角色
            if self._is_data_processing_worker():
                return ProcessRole.EXECUTOR
            else:
                return ProcessRole.HELPER
        elif process_type == ProcessType.CHILD:
            # 根据父进程和命令行参数判断子进程角色
            if self._is_helper_child():
                return ProcessRole.HELPER
            else:
                return ProcessRole.EXECUTOR
        else:
            return ProcessRole.STANDALONE

    def _is_data_processing_worker(self) -> bool:
        """
        检查是否是数据处理工作进程

        Returns:
            bool: 如果是数据处理工作进程返回True
        """
        # 检查命令行参数或环境变量
        cmdline = " ".join(self._environment_info.get("cmdline", []))
        data_processing_indicators = [
            "parquet", "storage", "download", "process", "data",
            "save_to_partition", "multiprocessing"
        ]

        return any(indicator in cmdline.lower() for indicator in data_processing_indicators)

    def _is_helper_child(self) -> bool:
        """
        检查是否是辅助子进程

        Returns:
            bool: 如果是辅助子进程返回True
        """
        # 检查父进程信息
        parent_name = self._environment_info.get("parent_name", "")
        if parent_name:
            helper_indicators = ["python", "jupyter", "ipython", "notebook"]
            return any(indicator in parent_name.lower() for indicator in helper_indicators)

        return False

    def get_role_responsibilities(self, role: ProcessRole) -> Dict[str, Any]:
        """
        获取角色职责描述

        Args:
            role: 进程角色

        Returns:
            Dict[str, Any]: 角色职责字典
        """
        responsibilities = {
            ProcessRole.COORDINATOR: {
                "description": "主协调进程",
                "responsibilities": [
                    "管理全局状态",
                    "协调子进程",
                    "处理用户交互",
                    "记录完整日志",
                    "收集系统统计信息"
                ],
                "log_level": "DEBUG",
                "log_features": ["full_logging", "console_output", "file_output", "stats_collection"],
                "performance_priority": "completeness"
            },
            ProcessRole.EXECUTOR: {
                "description": "任务执行进程",
                "responsibilities": [
                    "执行具体任务",
                    "处理数据",
                    "报告执行状态",
                    "最小化资源消耗",
                    "快速完成任务"
                ],
                "log_level": "WARNING",
                "log_features": ["minimal_logging", "file_output_only", "error_reporting"],
                "performance_priority": "speed"
            },
            ProcessRole.HELPER: {
                "description": "辅助进程",
                "responsibilities": [
                    "提供辅助功能",
                    "支持主进程",
                    "处理特定任务",
                    "适度日志记录",
                    "维护稳定性"
                ],
                "log_level": "INFO",
                "log_features": ["moderate_logging", "file_output", "status_reporting"],
                "performance_priority": "stability"
            },
            ProcessRole.STANDALONE: {
                "description": "独立进程",
                "responsibilities": [
                    "独立运行",
                    "自我管理",
                    "完整功能",
                    "标准日志记录",
                    "用户交互"
                ],
                "log_level": "INFO",
                "log_features": ["standard_logging", "console_output", "file_output"],
                "performance_priority": "balance"
            }
        }

        return responsibilities.get(role, responsibilities[ProcessRole.STANDALONE])

    def should_enable_feature(self, feature: str, role: Optional[ProcessRole] = None) -> bool:
        """
        检查是否应该启用特定功能

        Args:
            feature: 功能名称
            role: 进程角色，如果为None则自动识别

        Returns:
            bool: 如果应该启用返回True
        """
        if role is None:
            role = self.identify_role()

        responsibilities = self.get_role_responsibilities(role)
        log_features = responsibilities.get("log_features", [])

        feature_mapping = {
            "console_output": "console_output" in log_features,
            "file_output": "file_output" in log_features or "file_output_only" in log_features,
            "full_logging": "full_logging" in log_features,
            "minimal_logging": "minimal_logging" in log_features,
            "moderate_logging": "moderate_logging" in log_features,
            "standard_logging": "standard_logging" in log_features,
            "debug_logs": role in [ProcessRole.COORDINATOR],
            "init_logs": role in [ProcessRole.COORDINATOR, ProcessRole.STANDALONE],
            "stats_collection": "stats_collection" in log_features,
            "error_reporting": "error_reporting" in log_features or role == ProcessRole.COORDINATOR,
            "status_reporting": "status_reporting" in log_features or role != ProcessRole.EXECUTOR
        }

        return feature_mapping.get(feature, False)

    def get_performance_priority(self, role: Optional[ProcessRole] = None) -> str:
        """
        获取性能优先级

        Args:
            role: 进程角色，如果为None则自动识别

        Returns:
            str: 性能优先级 ("speed", "completeness", "stability", "balance")
        """
        if role is None:
            role = self.identify_role()

        responsibilities = self.get_role_responsibilities(role)
        return responsibilities.get("performance_priority", "balance")

    def get_environment_info(self) -> Dict[str, Any]:
        """
        获取环境信息

        Returns:
            Dict[str, Any]: 环境信息字典
        """
        return self._environment_info.copy()


# 全局角色识别器实例
_role_identifier_instance = None


def get_process_role_identifier() -> ProcessRoleIdentifier:
    """
    获取全局进程角色识别器实例

    Returns:
        ProcessRoleIdentifier: 角色识别器实例
    """
    global _role_identifier_instance
    if _role_identifier_instance is None:
        _role_identifier_instance = ProcessRoleIdentifier(get_process_detector())
    return _role_identifier_instance


def get_current_process_role() -> ProcessRole:
    """
    便利函数：获取当前进程角色

    Returns:
        ProcessRole: 当前进程角色
    """
    return get_process_role_identifier().identify_role()


def should_enable_feature(feature: str) -> bool:
    """
    便利函数：检查是否应该启用特定功能

    Args:
        feature: 功能名称

    Returns:
        bool: 如果应该启用返回True
    """
    return get_process_role_identifier().should_enable_feature(feature)
