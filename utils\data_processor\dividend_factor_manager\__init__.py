"""
复权因子管理器模块

该模块提供统一的复权因子获取接口，包括：
1. 本地复权因子覆盖范围检查
2. 智能更新策略
3. 统一的复权因子获取接口
4. 错误处理和降级策略

设计目标：
- 简化其他模块的调用逻辑
- 提供智能的数据获取策略
- 避免不必要的API调用
- 隐藏内部复杂性

使用示例：
    from utils.data_processor.dividend_factor_manager import dividend_factor_manager
    
    # 获取复权因子数据（自动检查覆盖范围和智能更新）
    factors = dividend_factor_manager.get_dividend_factors(
        symbol="000001.SZ",
        start_date="20240101",
        end_date="20241231"
    )
"""

from .dividend_factor_manager import DividendFactorManager

# 创建全局实例
dividend_factor_manager = DividendFactorManager()

# 导出主要接口
__all__ = [
    'dividend_factor_manager',
    'DividendFactorManager'
]
