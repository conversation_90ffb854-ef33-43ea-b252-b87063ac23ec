#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志收集服务模块

提供独立的日志收集服务，统一管理所有进程的日志输出
基于现有的进程池管理模式，复用项目现有的基础设施
"""

import os
import sys
import time
import threading
import multiprocessing
import queue
from typing import Dict, List, Optional, Any, Callable
from collections import defaultdict
import logging
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的模块
from utils.logger.log_aggregator import LogEntry, get_log_aggregator
from utils.logger.intelligent_log_router import get_intelligent_log_router, LogRoute
from utils.logger.process_detector import get_process_detector
from utils.multiprocessing.global_process_pool import get_global_process_pool


class LogCollectionService:
    """
    日志收集服务
    
    基于现有的进程池管理模式，提供独立的日志收集和处理服务
    """
    
    def __init__(self, 
                 max_queue_size: int = 50000,
                 worker_count: int = 2,
                 batch_size: int = 500,
                 flush_interval: float = 1.0):
        """
        初始化日志收集服务
        
        Args:
            max_queue_size: 最大队列大小
            worker_count: 工作线程数量
            batch_size: 批处理大小
            flush_interval: 刷新间隔（秒）
        """
        self.max_queue_size = max_queue_size
        self.worker_count = worker_count
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        
        # 日志队列（进程间共享）
        self.log_queue = multiprocessing.Queue(maxsize=max_queue_size)
        
        # 控制信号
        self.shutdown_event = multiprocessing.Event()
        self.is_running = False
        
        # 工作进程
        self.worker_processes = []
        
        # 统计信息（进程间共享）
        self.stats_manager = multiprocessing.Manager()
        self.stats = self.stats_manager.dict({
            'total_collected': 0,
            'total_processed': 0,
            'total_dropped': 0,
            'by_process_type': self.stats_manager.dict(),
            'by_route': self.stats_manager.dict(),
            'by_level': self.stats_manager.dict(),
            'start_time': time.time(),
            'last_activity': time.time()
        })
        
        # 处理器注册表
        self.processors: List[Callable[[List[LogEntry]], None]] = []
        
        # 获取现有组件
        self.process_detector = get_process_detector()
        self.router = get_intelligent_log_router()
    
    def add_processor(self, processor: Callable[[List[LogEntry]], None]):
        """
        添加日志处理器
        
        Args:
            processor: 日志处理函数
        """
        self.processors.append(processor)
    
    def remove_processor(self, processor: Callable[[List[LogEntry]], None]):
        """
        移除日志处理器
        
        Args:
            processor: 要移除的日志处理函数
        """
        if processor in self.processors:
            self.processors.remove(processor)
    
    def submit_log(self, log_entry: LogEntry) -> bool:
        """
        提交日志条目到收集服务
        
        Args:
            log_entry: 日志条目
            
        Returns:
            bool: 提交成功返回True
        """
        if not self.is_running:
            return False
        
        try:
            # 尝试添加到队列
            self.log_queue.put_nowait(log_entry)
            
            # 更新统计
            self.stats['total_collected'] += 1
            self.stats['last_activity'] = time.time()
            
            # 更新进程类型统计
            process_type = log_entry.process_type
            if process_type not in self.stats['by_process_type']:
                self.stats['by_process_type'][process_type] = 0
            self.stats['by_process_type'][process_type] += 1
            
            # 更新级别统计
            level_name = logging.getLevelName(log_entry.level)
            if level_name not in self.stats['by_level']:
                self.stats['by_level'][level_name] = 0
            self.stats['by_level'][level_name] += 1
            
            return True
            
        except queue.Full:
            # 队列满了，丢弃日志
            self.stats['total_dropped'] += 1
            return False
        except Exception:
            # 其他异常
            self.stats['total_dropped'] += 1
            return False
    
    def start(self):
        """启动日志收集服务"""
        if self.is_running:
            return
        
        self.is_running = True
        self.shutdown_event.clear()
        
        # 启动工作进程
        for i in range(self.worker_count):
            worker = multiprocessing.Process(
                target=self._worker_process,
                args=(i,),
                name=f"LogCollectionWorker-{i}"
            )
            worker.start()
            self.worker_processes.append(worker)
        
        print(f"日志收集服务已启动，工作进程数: {self.worker_count}")
    
    def stop(self, timeout: float = 10.0):
        """
        停止日志收集服务
        
        Args:
            timeout: 停止超时时间
        """
        if not self.is_running:
            return
        
        print("正在停止日志收集服务...")
        
        self.is_running = False
        self.shutdown_event.set()
        
        # 等待工作进程结束
        for worker in self.worker_processes:
            worker.join(timeout=timeout / len(self.worker_processes))
            if worker.is_alive():
                print(f"强制终止工作进程: {worker.name}")
                worker.terminate()
                worker.join(timeout=1.0)
        
        self.worker_processes.clear()
        print("日志收集服务已停止")
    
    def _worker_process(self, worker_id: int):
        """
        工作进程主函数
        
        Args:
            worker_id: 工作进程ID
        """
        print(f"日志收集工作进程 {worker_id} 已启动")
        
        batch = []
        last_flush_time = time.time()
        
        try:
            while not self.shutdown_event.is_set():
                try:
                    # 尝试获取日志条目
                    try:
                        log_entry = self.log_queue.get(timeout=0.1)
                        batch.append(log_entry)
                    except queue.Empty:
                        # 队列为空，检查是否需要刷新批次
                        current_time = time.time()
                        if batch and (current_time - last_flush_time >= self.flush_interval):
                            self._process_batch(batch, worker_id)
                            batch = []
                            last_flush_time = current_time
                        continue
                    
                    # 检查是否达到批处理大小
                    if len(batch) >= self.batch_size:
                        self._process_batch(batch, worker_id)
                        batch = []
                        last_flush_time = time.time()
                        
                except Exception as e:
                    print(f"工作进程 {worker_id} 处理异常: {e}")
                    continue
            
            # 处理剩余的日志
            if batch:
                self._process_batch(batch, worker_id)
                
        except Exception as e:
            print(f"工作进程 {worker_id} 发生严重异常: {e}")
        finally:
            print(f"日志收集工作进程 {worker_id} 已退出")
    
    def _process_batch(self, batch: List[LogEntry], worker_id: int):
        """
        处理日志批次
        
        Args:
            batch: 日志条目列表
            worker_id: 工作进程ID
        """
        if not batch:
            return
        
        try:
            # 路由日志
            routed_logs = defaultdict(list)
            for log_entry in batch:
                route = self.router.route_log(log_entry)
                routed_logs[route].append(log_entry)
                
                # 更新路由统计
                route_name = route.value
                if route_name not in self.stats['by_route']:
                    self.stats['by_route'][route_name] = 0
                self.stats['by_route'][route_name] += 1
            
            # 处理不同路由的日志
            for route, logs in routed_logs.items():
                if route == LogRoute.DISCARD:
                    continue  # 丢弃的日志不处理
                
                # 调用注册的处理器
                for processor in self.processors:
                    try:
                        processor(logs)
                    except Exception as e:
                        print(f"日志处理器异常 (工作进程 {worker_id}): {e}")
            
            # 更新处理统计
            self.stats['total_processed'] += len(batch)
            
        except Exception as e:
            print(f"处理日志批次异常 (工作进程 {worker_id}): {e}")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        current_time = time.time()
        uptime = current_time - self.stats['start_time']
        
        return {
            'is_running': self.is_running,
            'worker_count': len(self.worker_processes),
            'queue_size': self.log_queue.qsize() if hasattr(self.log_queue, 'qsize') else 0,
            'uptime': uptime,
            'total_collected': self.stats['total_collected'],
            'total_processed': self.stats['total_processed'],
            'total_dropped': self.stats['total_dropped'],
            'processing_rate': self.stats['total_processed'] / max(1, uptime),
            'drop_rate': (self.stats['total_dropped'] / max(1, self.stats['total_collected'])) * 100,
            'by_process_type': dict(self.stats['by_process_type']),
            'by_route': dict(self.stats['by_route']),
            'by_level': dict(self.stats['by_level']),
            'last_activity': self.stats['last_activity']
        }
    
    def clear_stats(self):
        """清空统计信息"""
        self.stats.update({
            'total_collected': 0,
            'total_processed': 0,
            'total_dropped': 0,
            'by_process_type': self.stats_manager.dict(),
            'by_route': self.stats_manager.dict(),
            'by_level': self.stats_manager.dict(),
            'start_time': time.time(),
            'last_activity': time.time()
        })


# 全局收集服务实例
_collection_service_instance = None


def get_log_collection_service() -> LogCollectionService:
    """
    获取全局日志收集服务实例
    
    Returns:
        LogCollectionService: 收集服务实例
    """
    global _collection_service_instance
    if _collection_service_instance is None:
        _collection_service_instance = LogCollectionService()
    return _collection_service_instance


def start_log_collection_service(**kwargs):
    """
    便利函数：启动日志收集服务
    
    Args:
        **kwargs: 服务配置参数
    """
    service = get_log_collection_service()
    
    # 应用配置参数
    for key, value in kwargs.items():
        if hasattr(service, key):
            setattr(service, key, value)
    
    service.start()


def stop_log_collection_service(timeout: float = 10.0):
    """
    便利函数：停止日志收集服务
    
    Args:
        timeout: 停止超时时间
    """
    service = get_log_collection_service()
    service.stop(timeout)
