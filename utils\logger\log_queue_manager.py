#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志队列管理器模块

提供高并发日志写入的队列管理功能
复用现有的multiprocessing基础设施
"""

import os
import sys
import time
import threading
import multiprocessing
import queue
from typing import Dict, List, Optional, Any, Union
from collections import defaultdict
import logging
import pickle

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的模块
from utils.logger.log_aggregator import LogEntry
from utils.logger.process_detector import get_process_detector


class LogQueueManager:
    """
    日志队列管理器
    
    处理高并发日志写入，提供多种队列策略
    复用现有的multiprocessing基础设施
    """
    
    def __init__(self, 
                 queue_type: str = "multiprocessing",
                 max_size: int = 10000,
                 overflow_strategy: str = "drop_oldest",
                 enable_compression: bool = False):
        """
        初始化日志队列管理器
        
        Args:
            queue_type: 队列类型 ("multiprocessing", "threading", "hybrid")
            max_size: 最大队列大小
            overflow_strategy: 溢出策略 ("drop_oldest", "drop_newest", "block", "expand")
            enable_compression: 是否启用压缩
        """
        self.queue_type = queue_type
        self.max_size = max_size
        self.overflow_strategy = overflow_strategy
        self.enable_compression = enable_compression
        
        # 创建队列
        self.queues = self._create_queues()
        
        # 队列选择策略
        self.queue_selector = self._create_queue_selector()
        
        # 统计信息
        self.stats = {
            'total_enqueued': 0,
            'total_dequeued': 0,
            'total_dropped': 0,
            'total_compressed': 0,
            'by_queue': defaultdict(lambda: {'enqueued': 0, 'dequeued': 0, 'dropped': 0}),
            'by_process_type': defaultdict(int),
            'overflow_events': 0,
            'compression_ratio': 0.0
        }
        
        # 锁
        self.stats_lock = threading.Lock()
        
        # 进程检测器
        self.process_detector = get_process_detector()
    
    def _create_queues(self) -> Dict[str, Any]:
        """
        创建队列
        
        Returns:
            Dict[str, Any]: 队列字典
        """
        queues = {}
        
        if self.queue_type == "multiprocessing":
            # 多进程队列
            queues['main'] = multiprocessing.Queue(maxsize=self.max_size)
            
        elif self.queue_type == "threading":
            # 多线程队列
            queues['main'] = queue.Queue(maxsize=self.max_size)
            
        elif self.queue_type == "hybrid":
            # 混合模式：主进程使用线程队列，工作进程使用进程队列
            queues['main'] = multiprocessing.Queue(maxsize=self.max_size)
            queues['thread'] = queue.Queue(maxsize=self.max_size // 2)
            
        else:
            raise ValueError(f"不支持的队列类型: {self.queue_type}")
        
        return queues
    
    def _create_queue_selector(self) -> callable:
        """
        创建队列选择器
        
        Returns:
            callable: 队列选择函数
        """
        if self.queue_type == "hybrid":
            def selector(log_entry: LogEntry) -> str:
                # 主进程使用线程队列，工作进程使用进程队列
                if self.process_detector.is_main_process():
                    return 'thread'
                else:
                    return 'main'
        else:
            def selector(log_entry: LogEntry) -> str:
                return 'main'
        
        return selector
    
    def enqueue(self, log_entry: LogEntry) -> bool:
        """
        将日志条目加入队列
        
        Args:
            log_entry: 日志条目
            
        Returns:
            bool: 入队成功返回True
        """
        # 选择队列
        queue_name = self.queue_selector(log_entry)
        selected_queue = self.queues[queue_name]
        
        # 准备数据
        data = self._prepare_data(log_entry)
        
        try:
            # 尝试入队
            if hasattr(selected_queue, 'put_nowait'):
                selected_queue.put_nowait(data)
            else:
                selected_queue.put(data, block=False)
            
            # 更新统计
            with self.stats_lock:
                self.stats['total_enqueued'] += 1
                self.stats['by_queue'][queue_name]['enqueued'] += 1
                self.stats['by_process_type'][log_entry.process_type] += 1
            
            return True
            
        except (queue.Full, Exception):
            # 队列满或其他异常，应用溢出策略
            return self._handle_overflow(selected_queue, queue_name, data, log_entry)
    
    def dequeue(self, timeout: Optional[float] = None) -> Optional[LogEntry]:
        """
        从队列中取出日志条目
        
        Args:
            timeout: 超时时间
            
        Returns:
            Optional[LogEntry]: 日志条目，如果队列为空返回None
        """
        # 按优先级尝试各个队列
        queue_priority = ['thread', 'main'] if 'thread' in self.queues else ['main']
        
        for queue_name in queue_priority:
            if queue_name not in self.queues:
                continue
                
            selected_queue = self.queues[queue_name]
            
            try:
                if timeout is None:
                    # 非阻塞获取
                    if hasattr(selected_queue, 'get_nowait'):
                        data = selected_queue.get_nowait()
                    else:
                        data = selected_queue.get(block=False)
                else:
                    # 阻塞获取
                    data = selected_queue.get(timeout=timeout)
                
                # 恢复数据
                log_entry = self._restore_data(data)
                
                # 更新统计
                with self.stats_lock:
                    self.stats['total_dequeued'] += 1
                    self.stats['by_queue'][queue_name]['dequeued'] += 1
                
                return log_entry
                
            except (queue.Empty, Exception):
                continue
        
        return None
    
    def dequeue_batch(self, batch_size: int = 100, timeout: float = 1.0) -> List[LogEntry]:
        """
        批量从队列中取出日志条目
        
        Args:
            batch_size: 批次大小
            timeout: 总超时时间
            
        Returns:
            List[LogEntry]: 日志条目列表
        """
        batch = []
        start_time = time.time()
        
        while len(batch) < batch_size and (time.time() - start_time) < timeout:
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time <= 0:
                break
            
            log_entry = self.dequeue(timeout=min(0.1, remaining_time))
            if log_entry:
                batch.append(log_entry)
            else:
                # 如果没有获取到数据，稍微等待一下
                time.sleep(0.01)
        
        return batch
    
    def _prepare_data(self, log_entry: LogEntry) -> Union[LogEntry, bytes]:
        """
        准备数据（可能包括压缩）
        
        Args:
            log_entry: 日志条目
            
        Returns:
            Union[LogEntry, bytes]: 准备好的数据
        """
        if not self.enable_compression:
            return log_entry
        
        try:
            # 序列化并压缩
            import zlib
            serialized = pickle.dumps(log_entry)
            compressed = zlib.compress(serialized)
            
            # 计算压缩比
            compression_ratio = len(compressed) / len(serialized)
            
            with self.stats_lock:
                self.stats['total_compressed'] += 1
                # 更新平均压缩比
                total_compressed = self.stats['total_compressed']
                current_ratio = self.stats['compression_ratio']
                self.stats['compression_ratio'] = (
                    (current_ratio * (total_compressed - 1) + compression_ratio) / total_compressed
                )
            
            return compressed
            
        except Exception:
            # 压缩失败，返回原始数据
            return log_entry
    
    def _restore_data(self, data: Union[LogEntry, bytes]) -> LogEntry:
        """
        恢复数据（可能包括解压缩）
        
        Args:
            data: 数据
            
        Returns:
            LogEntry: 日志条目
        """
        if isinstance(data, LogEntry):
            return data
        
        try:
            # 解压缩并反序列化
            import zlib
            decompressed = zlib.decompress(data)
            log_entry = pickle.loads(decompressed)
            return log_entry
            
        except Exception:
            # 解压缩失败，可能是未压缩的数据
            if isinstance(data, bytes):
                try:
                    return pickle.loads(data)
                except Exception:
                    pass
            
            # 无法恢复，返回空的日志条目
            return LogEntry(
                timestamp=time.time(),
                level=logging.ERROR,
                message="无法恢复日志数据",
                module="log_queue_manager",
                function="_restore_data",
                line_number=0,
                process_id=os.getpid(),
                process_type="unknown",
                thread_id=threading.get_ident()
            )
    
    def _handle_overflow(self, selected_queue: Any, queue_name: str, 
                        data: Any, log_entry: LogEntry) -> bool:
        """
        处理队列溢出
        
        Args:
            selected_queue: 选中的队列
            queue_name: 队列名称
            data: 数据
            log_entry: 日志条目
            
        Returns:
            bool: 处理成功返回True
        """
        with self.stats_lock:
            self.stats['overflow_events'] += 1
            self.stats['by_queue'][queue_name]['dropped'] += 1
        
        if self.overflow_strategy == "drop_oldest":
            # 丢弃最旧的数据，添加新数据
            try:
                if hasattr(selected_queue, 'get_nowait'):
                    selected_queue.get_nowait()  # 移除最旧的
                else:
                    selected_queue.get(block=False)
                
                # 添加新数据
                if hasattr(selected_queue, 'put_nowait'):
                    selected_queue.put_nowait(data)
                else:
                    selected_queue.put(data, block=False)
                
                with self.stats_lock:
                    self.stats['total_enqueued'] += 1
                    self.stats['by_queue'][queue_name]['enqueued'] += 1
                
                return True
                
            except Exception:
                pass
        
        elif self.overflow_strategy == "drop_newest":
            # 丢弃新数据
            pass
        
        elif self.overflow_strategy == "block":
            # 阻塞等待
            try:
                selected_queue.put(data, timeout=1.0)
                
                with self.stats_lock:
                    self.stats['total_enqueued'] += 1
                    self.stats['by_queue'][queue_name]['enqueued'] += 1
                
                return True
                
            except Exception:
                pass
        
        elif self.overflow_strategy == "expand":
            # 扩展队列（仅对某些队列类型有效）
            # 这里简化处理，实际可能需要重新创建队列
            pass
        
        # 溢出处理失败
        with self.stats_lock:
            self.stats['total_dropped'] += 1
        
        return False
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """
        获取队列统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self.stats_lock:
            queue_sizes = {}
            for name, q in self.queues.items():
                try:
                    if hasattr(q, 'qsize'):
                        queue_sizes[name] = q.qsize()
                    else:
                        queue_sizes[name] = 0
                except Exception:
                    queue_sizes[name] = -1  # 无法获取大小
            
            return {
                'queue_type': self.queue_type,
                'max_size': self.max_size,
                'overflow_strategy': self.overflow_strategy,
                'enable_compression': self.enable_compression,
                'queue_sizes': queue_sizes,
                'total_enqueued': self.stats['total_enqueued'],
                'total_dequeued': self.stats['total_dequeued'],
                'total_dropped': self.stats['total_dropped'],
                'total_compressed': self.stats['total_compressed'],
                'compression_ratio': self.stats['compression_ratio'],
                'overflow_events': self.stats['overflow_events'],
                'by_queue': dict(self.stats['by_queue']),
                'by_process_type': dict(self.stats['by_process_type'])
            }
    
    def clear_stats(self):
        """清空统计信息"""
        with self.stats_lock:
            self.stats = {
                'total_enqueued': 0,
                'total_dequeued': 0,
                'total_dropped': 0,
                'total_compressed': 0,
                'by_queue': defaultdict(lambda: {'enqueued': 0, 'dequeued': 0, 'dropped': 0}),
                'by_process_type': defaultdict(int),
                'overflow_events': 0,
                'compression_ratio': 0.0
            }
    
    def close(self):
        """关闭队列管理器"""
        for queue_name, q in self.queues.items():
            try:
                if hasattr(q, 'close'):
                    q.close()
                if hasattr(q, 'join_thread'):
                    q.join_thread()
            except Exception:
                pass


# 全局队列管理器实例
_queue_manager_instance = None


def get_log_queue_manager() -> LogQueueManager:
    """
    获取全局日志队列管理器实例
    
    Returns:
        LogQueueManager: 队列管理器实例
    """
    global _queue_manager_instance
    if _queue_manager_instance is None:
        _queue_manager_instance = LogQueueManager()
    return _queue_manager_instance
