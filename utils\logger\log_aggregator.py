#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志聚合器模块

提供进程间日志收集和聚合功能，基于现有的TargetFilter扩展
复用项目现有的过滤器和配置机制
"""

import os
import sys
import time
import threading
import queue
from typing import Dict, List, Optional, Set, Callable
from collections import defaultdict, deque
from dataclasses import dataclass
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的日志模块
from utils.logger.config import LogTarget
from utils.logger.filters import TargetFilter
from utils.logger.process_detector import get_process_detector, ProcessType


@dataclass
class LogEntry:
    """日志条目数据类"""
    timestamp: float
    level: int
    message: str
    module: str
    function: str
    line_number: int
    process_id: int
    process_type: str
    thread_id: int
    extra_data: Dict = None


class LogAggregator:
    """
    日志聚合器
    
    基于现有的TargetFilter扩展，提供进程间日志收集和聚合功能
    复用项目现有的过滤器机制
    """
    
    def __init__(self, max_queue_size: int = 10000, batch_size: int = 100):
        """
        初始化日志聚合器
        
        Args:
            max_queue_size: 最大队列大小
            batch_size: 批处理大小
        """
        self.max_queue_size = max_queue_size
        self.batch_size = batch_size
        
        # 日志队列
        self.log_queue = queue.Queue(maxsize=max_queue_size)
        
        # 聚合统计
        self.stats = {
            'total_logs': 0,
            'processed_logs': 0,
            'dropped_logs': 0,
            'by_process_type': defaultdict(int),
            'by_level': defaultdict(int),
            'by_module': defaultdict(int)
        }
        
        # 进程检测器
        self.process_detector = get_process_detector()
        
        # 聚合器状态
        self.is_running = False
        self.worker_thread = None
        self.shutdown_event = threading.Event()
        
        # 注册的处理器
        self.handlers: List[Callable[[List[LogEntry]], None]] = []
        
        # 最近日志缓存（用于去重）
        self.recent_logs = deque(maxlen=1000)
        self.recent_logs_set = set()
    
    def add_handler(self, handler: Callable[[List[LogEntry]], None]):
        """
        添加日志处理器
        
        Args:
            handler: 日志处理函数，接收LogEntry列表
        """
        self.handlers.append(handler)
    
    def remove_handler(self, handler: Callable[[List[LogEntry]], None]):
        """
        移除日志处理器
        
        Args:
            handler: 要移除的日志处理函数
        """
        if handler in self.handlers:
            self.handlers.remove(handler)
    
    def submit_log(self, record: logging.LogRecord) -> bool:
        """
        提交日志记录到聚合器
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 提交成功返回True
        """
        try:
            # 创建日志条目
            log_entry = LogEntry(
                timestamp=record.created,
                level=record.levelno,
                message=record.getMessage(),
                module=record.name,
                function=record.funcName,
                line_number=record.lineno,
                process_id=os.getpid(),
                process_type=self.process_detector.detect_process_type().value,
                thread_id=record.thread,
                extra_data=getattr(record, 'extra_data', None)
            )
            
            # 检查是否应该过滤此日志
            if self._should_filter_log(log_entry):
                return False
            
            # 尝试添加到队列
            try:
                self.log_queue.put_nowait(log_entry)
                self.stats['total_logs'] += 1
                self.stats['by_process_type'][log_entry.process_type] += 1
                self.stats['by_level'][record.levelname] += 1
                self.stats['by_module'][log_entry.module] += 1
                return True
            except queue.Full:
                # 队列满了，丢弃日志
                self.stats['dropped_logs'] += 1
                return False
                
        except Exception:
            # 提交失败
            return False
    
    def _should_filter_log(self, log_entry: LogEntry) -> bool:
        """
        检查是否应该过滤此日志
        
        Args:
            log_entry: 日志条目
            
        Returns:
            bool: 如果应该过滤返回True
        """
        # 基于进程类型的过滤
        if log_entry.process_type == ProcessType.WORKER.value:
            # 工作进程只保留WARNING及以上级别的日志
            if log_entry.level < logging.WARNING:
                return True
            
            # 过滤初始化相关的日志
            init_keywords = ['初始化完成', '初始化成功', '已注册', '已加载', '版本:', '提供']
            if any(keyword in log_entry.message for keyword in init_keywords):
                return True
        
        # 去重检查
        log_signature = f"{log_entry.module}:{log_entry.message}"
        if log_signature in self.recent_logs_set:
            return True
        
        # 添加到最近日志缓存
        if len(self.recent_logs) >= self.recent_logs.maxlen:
            # 移除最旧的日志签名
            oldest_entry = self.recent_logs[0]
            oldest_signature = f"{oldest_entry.module}:{oldest_entry.message}"
            self.recent_logs_set.discard(oldest_signature)
        
        self.recent_logs.append(log_entry)
        self.recent_logs_set.add(log_signature)
        
        return False
    
    def start(self):
        """启动日志聚合器"""
        if self.is_running:
            return
        
        self.is_running = True
        self.shutdown_event.clear()
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
    
    def stop(self, timeout: float = 5.0):
        """
        停止日志聚合器
        
        Args:
            timeout: 停止超时时间
        """
        if not self.is_running:
            return
        
        self.is_running = False
        self.shutdown_event.set()
        
        if self.worker_thread:
            self.worker_thread.join(timeout=timeout)
    
    def _worker_loop(self):
        """工作线程主循环"""
        batch = []
        
        while self.is_running or not self.log_queue.empty():
            try:
                # 尝试获取日志条目
                try:
                    log_entry = self.log_queue.get(timeout=0.1)
                    batch.append(log_entry)
                except queue.Empty:
                    # 队列为空，处理当前批次
                    if batch:
                        self._process_batch(batch)
                        batch = []
                    continue
                
                # 检查是否达到批处理大小
                if len(batch) >= self.batch_size:
                    self._process_batch(batch)
                    batch = []
                    
            except Exception as e:
                # 处理异常，继续运行
                print(f"日志聚合器工作线程异常: {e}")
                continue
        
        # 处理剩余的日志
        if batch:
            self._process_batch(batch)
    
    def _process_batch(self, batch: List[LogEntry]):
        """
        处理日志批次
        
        Args:
            batch: 日志条目列表
        """
        if not batch:
            return
        
        try:
            # 调用所有注册的处理器
            for handler in self.handlers:
                try:
                    handler(batch)
                except Exception as e:
                    print(f"日志处理器异常: {e}")
            
            # 更新统计
            self.stats['processed_logs'] += len(batch)
            
        except Exception as e:
            print(f"处理日志批次异常: {e}")
    
    def get_stats(self) -> Dict:
        """
        获取聚合器统计信息
        
        Returns:
            Dict: 统计信息字典
        """
        return {
            'total_logs': self.stats['total_logs'],
            'processed_logs': self.stats['processed_logs'],
            'dropped_logs': self.stats['dropped_logs'],
            'queue_size': self.log_queue.qsize(),
            'by_process_type': dict(self.stats['by_process_type']),
            'by_level': dict(self.stats['by_level']),
            'by_module': dict(self.stats['by_module']),
            'is_running': self.is_running
        }
    
    def clear_stats(self):
        """清空统计信息"""
        self.stats = {
            'total_logs': 0,
            'processed_logs': 0,
            'dropped_logs': 0,
            'by_process_type': defaultdict(int),
            'by_level': defaultdict(int),
            'by_module': defaultdict(int)
        }


# 全局聚合器实例
_aggregator_instance = None


def get_log_aggregator() -> LogAggregator:
    """
    获取全局日志聚合器实例
    
    Returns:
        LogAggregator: 聚合器实例
    """
    global _aggregator_instance
    if _aggregator_instance is None:
        _aggregator_instance = LogAggregator()
    return _aggregator_instance
