#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能日志路由器模块

提供智能的日志路由和缓冲机制
复用现有的过滤器和配置系统
"""

import os
import sys
import time
import threading
import queue
from typing import Dict, List, Optional, Callable, Any
from collections import defaultdict, deque
from dataclasses import dataclass
from enum import Enum
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的模块
from utils.logger.config import LogTarget
from utils.logger.log_aggregator import LogEntry
from utils.logger.process_detector import get_process_detector, ProcessType, ProcessRole
from utils.logger.filters import IntelligentLogFilter


class LogRoute(Enum):
    """日志路由枚举"""
    FILE_ONLY = "file_only"
    CONSOLE_ONLY = "console_only"
    BOTH = "both"
    DISCARD = "discard"
    BUFFER = "buffer"


@dataclass
class RoutingRule:
    """路由规则数据类"""
    name: str
    condition: Callable[[LogEntry], bool]
    route: LogRoute
    priority: int = 0
    description: str = ""


class LogBuffer:
    """
    日志缓冲器
    
    提供批量处理和传输日志的缓冲机制
    """
    
    def __init__(self, max_size: int = 1000, flush_interval: float = 5.0):
        """
        初始化日志缓冲器
        
        Args:
            max_size: 最大缓冲大小
            flush_interval: 刷新间隔（秒）
        """
        self.max_size = max_size
        self.flush_interval = flush_interval
        
        # 缓冲区
        self.buffer = deque(maxlen=max_size)
        self.buffer_lock = threading.Lock()
        
        # 刷新控制
        self.last_flush_time = time.time()
        self.auto_flush_enabled = True
        
        # 统计信息
        self.stats = {
            'total_buffered': 0,
            'total_flushed': 0,
            'flush_count': 0,
            'overflow_count': 0
        }
    
    def add(self, log_entry: LogEntry) -> bool:
        """
        添加日志条目到缓冲区
        
        Args:
            log_entry: 日志条目
            
        Returns:
            bool: 添加成功返回True
        """
        with self.buffer_lock:
            if len(self.buffer) >= self.max_size:
                self.stats['overflow_count'] += 1
                return False
            
            self.buffer.append(log_entry)
            self.stats['total_buffered'] += 1
            
            # 检查是否需要自动刷新
            if self.auto_flush_enabled:
                current_time = time.time()
                if (len(self.buffer) >= self.max_size or 
                    current_time - self.last_flush_time >= self.flush_interval):
                    return self._flush_internal()
            
            return True
    
    def flush(self) -> List[LogEntry]:
        """
        刷新缓冲区
        
        Returns:
            List[LogEntry]: 刷新的日志条目列表
        """
        with self.buffer_lock:
            return self._flush_internal()
    
    def _flush_internal(self) -> List[LogEntry]:
        """
        内部刷新方法
        
        Returns:
            List[LogEntry]: 刷新的日志条目列表
        """
        if not self.buffer:
            return []
        
        flushed_entries = list(self.buffer)
        self.buffer.clear()
        
        self.stats['total_flushed'] += len(flushed_entries)
        self.stats['flush_count'] += 1
        self.last_flush_time = time.time()
        
        return flushed_entries
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓冲器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self.buffer_lock:
            return {
                'buffer_size': len(self.buffer),
                'max_size': self.max_size,
                'total_buffered': self.stats['total_buffered'],
                'total_flushed': self.stats['total_flushed'],
                'flush_count': self.stats['flush_count'],
                'overflow_count': self.stats['overflow_count'],
                'last_flush_time': self.last_flush_time
            }


class LogPriorityManager:
    """
    日志优先级管理器
    
    动态调整不同模块的日志级别
    """
    
    def __init__(self):
        """初始化日志优先级管理器"""
        self.module_levels = {}
        self.process_levels = {}
        self.global_level = logging.INFO
        
        # 默认优先级规则
        self.priority_rules = {
            'error_modules': {
                'level': logging.ERROR,
                'modules': ['error', 'exception', 'critical']
            },
            'debug_modules': {
                'level': logging.DEBUG,
                'modules': ['debug', 'trace', 'verbose']
            },
            'performance_modules': {
                'level': logging.WARNING,
                'modules': ['performance', 'timing', 'benchmark']
            }
        }
    
    def set_module_level(self, module_name: str, level: int):
        """
        设置模块日志级别
        
        Args:
            module_name: 模块名称
            level: 日志级别
        """
        self.module_levels[module_name] = level
    
    def set_process_level(self, process_type: str, level: int):
        """
        设置进程类型日志级别
        
        Args:
            process_type: 进程类型
            level: 日志级别
        """
        self.process_levels[process_type] = level
    
    def get_effective_level(self, log_entry: LogEntry) -> int:
        """
        获取有效的日志级别
        
        Args:
            log_entry: 日志条目
            
        Returns:
            int: 有效的日志级别
        """
        # 检查模块特定级别
        if log_entry.module in self.module_levels:
            return self.module_levels[log_entry.module]
        
        # 检查进程类型特定级别
        if log_entry.process_type in self.process_levels:
            return self.process_levels[log_entry.process_type]
        
        # 检查优先级规则
        for rule_name, rule_config in self.priority_rules.items():
            if any(keyword in log_entry.module.lower() for keyword in rule_config['modules']):
                return rule_config['level']
        
        # 返回全局级别
        return self.global_level
    
    def should_log(self, log_entry: LogEntry) -> bool:
        """
        检查是否应该记录日志
        
        Args:
            log_entry: 日志条目
            
        Returns:
            bool: 如果应该记录返回True
        """
        effective_level = self.get_effective_level(log_entry)
        return log_entry.level >= effective_level


class IntelligentLogRouter:
    """
    智能日志路由器
    
    根据日志内容和进程类型智能路由日志
    """
    
    def __init__(self):
        """初始化智能日志路由器"""
        self.process_detector = get_process_detector()
        self.priority_manager = LogPriorityManager()
        self.intelligent_filter = IntelligentLogFilter()
        
        # 路由规则
        self.routing_rules: List[RoutingRule] = []
        
        # 缓冲器
        self.buffers = {
            LogRoute.FILE_ONLY: LogBuffer(max_size=500, flush_interval=2.0),
            LogRoute.CONSOLE_ONLY: LogBuffer(max_size=100, flush_interval=1.0),
            LogRoute.BOTH: LogBuffer(max_size=200, flush_interval=1.5),
            LogRoute.BUFFER: LogBuffer(max_size=1000, flush_interval=10.0)
        }
        
        # 路由统计
        self.stats = {
            'total_routed': 0,
            'by_route': defaultdict(int),
            'by_process_type': defaultdict(int),
            'by_level': defaultdict(int)
        }
        
        # 初始化默认路由规则
        self._setup_default_routing_rules()
    
    def _setup_default_routing_rules(self):
        """设置默认路由规则"""
        # 错误日志总是路由到文件和控制台
        self.add_routing_rule(
            "error_logs",
            lambda entry: entry.level >= logging.ERROR,
            LogRoute.BOTH,
            priority=100,
            description="错误日志路由到文件和控制台"
        )
        
        # 工作进程的日志只路由到文件
        self.add_routing_rule(
            "worker_logs",
            lambda entry: entry.process_type == ProcessType.WORKER.value,
            LogRoute.FILE_ONLY,
            priority=80,
            description="工作进程日志只路由到文件"
        )
        
        # 主进程的INFO及以上日志路由到文件和控制台
        self.add_routing_rule(
            "main_info_logs",
            lambda entry: (entry.process_type == ProcessType.MAIN.value and 
                          entry.level >= logging.INFO),
            LogRoute.BOTH,
            priority=60,
            description="主进程INFO及以上日志路由到文件和控制台"
        )
        
        # 调试日志只路由到文件
        self.add_routing_rule(
            "debug_logs",
            lambda entry: entry.level == logging.DEBUG,
            LogRoute.FILE_ONLY,
            priority=40,
            description="调试日志只路由到文件"
        )
        
        # 默认规则：其他日志路由到文件
        self.add_routing_rule(
            "default",
            lambda entry: True,
            LogRoute.FILE_ONLY,
            priority=0,
            description="默认规则：其他日志路由到文件"
        )

    def add_routing_rule(self, name: str, condition: Callable[[LogEntry], bool],
                        route: LogRoute, priority: int = 0, description: str = ""):
        """
        添加路由规则

        Args:
            name: 规则名称
            condition: 条件函数
            route: 路由目标
            priority: 优先级（数值越大优先级越高）
            description: 规则描述
        """
        rule = RoutingRule(name, condition, route, priority, description)
        self.routing_rules.append(rule)

        # 按优先级排序
        self.routing_rules.sort(key=lambda r: r.priority, reverse=True)

    def route_log(self, log_entry: LogEntry) -> LogRoute:
        """
        路由日志条目

        Args:
            log_entry: 日志条目

        Returns:
            LogRoute: 路由目标
        """
        self.stats['total_routed'] += 1
        self.stats['by_process_type'][log_entry.process_type] += 1
        self.stats['by_level'][logging.getLevelName(log_entry.level)] += 1

        # 检查是否应该记录此日志
        if not self.priority_manager.should_log(log_entry):
            route = LogRoute.DISCARD
        else:
            # 应用路由规则
            route = self._apply_routing_rules(log_entry)

        self.stats['by_route'][route.value] += 1

        # 添加到相应的缓冲器
        if route in self.buffers:
            self.buffers[route].add(log_entry)

        return route

    def _apply_routing_rules(self, log_entry: LogEntry) -> LogRoute:
        """
        应用路由规则

        Args:
            log_entry: 日志条目

        Returns:
            LogRoute: 路由目标
        """
        for rule in self.routing_rules:
            try:
                if rule.condition(log_entry):
                    return rule.route
            except Exception:
                # 规则执行失败，继续下一个规则
                continue

        # 如果没有规则匹配，使用默认路由
        return LogRoute.FILE_ONLY

    def flush_buffers(self) -> Dict[LogRoute, List[LogEntry]]:
        """
        刷新所有缓冲器

        Returns:
            Dict[LogRoute, List[LogEntry]]: 刷新的日志条目字典
        """
        flushed_logs = {}
        for route, buffer in self.buffers.items():
            flushed_logs[route] = buffer.flush()
        return flushed_logs

    def get_routing_stats(self) -> Dict[str, Any]:
        """
        获取路由统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        buffer_stats = {}
        for route, buffer in self.buffers.items():
            buffer_stats[route.value] = buffer.get_stats()

        return {
            'routing': self.stats.copy(),
            'buffers': buffer_stats,
            'rules_count': len(self.routing_rules),
            'filter_stats': self.intelligent_filter.get_filter_stats()
        }

    def clear_stats(self):
        """清空统计信息"""
        self.stats = {
            'total_routed': 0,
            'by_route': defaultdict(int),
            'by_process_type': defaultdict(int),
            'by_level': defaultdict(int)
        }

        self.intelligent_filter.clear_stats()


# 全局路由器实例
_router_instance = None


def get_intelligent_log_router() -> IntelligentLogRouter:
    """
    获取全局智能日志路由器实例

    Returns:
        IntelligentLogRouter: 路由器实例
    """
    global _router_instance
    if _router_instance is None:
        _router_instance = IntelligentLogRouter()
    return _router_instance
    
    def add_routing_rule(self, name: str, condition: Callable[[LogEntry], bool], 
                        route: LogRoute, priority: int = 0, description: str = ""):
        """
        添加路由规则
        
        Args:
            name: 规则名称
            condition: 条件函数
            route: 路由目标
            priority: 优先级（数值越大优先级越高）
            description: 规则描述
        """
        rule = RoutingRule(name, condition, route, priority, description)
        self.routing_rules.append(rule)
        
        # 按优先级排序
        self.routing_rules.sort(key=lambda r: r.priority, reverse=True)
    
    def route_log(self, log_entry: LogEntry) -> LogRoute:
        """
        路由日志条目
        
        Args:
            log_entry: 日志条目
            
        Returns:
            LogRoute: 路由目标
        """
        self.stats['total_routed'] += 1
        self.stats['by_process_type'][log_entry.process_type] += 1
        self.stats['by_level'][logging.getLevelName(log_entry.level)] += 1
        
        # 检查是否应该记录此日志
        if not self.priority_manager.should_log(log_entry):
            route = LogRoute.DISCARD
        else:
            # 应用路由规则
            route = self._apply_routing_rules(log_entry)
        
        self.stats['by_route'][route.value] += 1
        
        # 添加到相应的缓冲器
        if route in self.buffers:
            self.buffers[route].add(log_entry)
        
        return route
    
    def _apply_routing_rules(self, log_entry: LogEntry) -> LogRoute:
        """
        应用路由规则
        
        Args:
            log_entry: 日志条目
            
        Returns:
            LogRoute: 路由目标
        """
        for rule in self.routing_rules:
            try:
                if rule.condition(log_entry):
                    return rule.route
            except Exception:
                # 规则执行失败，继续下一个规则
                continue
        
        # 如果没有规则匹配，使用默认路由
        return LogRoute.FILE_ONLY
    
    def flush_buffers(self) -> Dict[LogRoute, List[LogEntry]]:
        """
        刷新所有缓冲器
        
        Returns:
            Dict[LogRoute, List[LogEntry]]: 刷新的日志条目字典
        """
        flushed_logs = {}
        for route, buffer in self.buffers.items():
            flushed_logs[route] = buffer.flush()
        return flushed_logs
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """
        获取路由统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        buffer_stats = {}
        for route, buffer in self.buffers.items():
            buffer_stats[route.value] = buffer.get_stats()
        
        return {
            'routing': self.stats.copy(),
            'buffers': buffer_stats,
            'rules_count': len(self.routing_rules),
            'filter_stats': self.intelligent_filter.get_filter_stats()
        }
    
    def clear_stats(self):
        """清空统计信息"""
        self.stats = {
            'total_routed': 0,
            'by_route': defaultdict(int),
            'by_process_type': defaultdict(int),
            'by_level': defaultdict(int)
        }
        
        self.intelligent_filter.clear_stats()


# 全局路由器实例
_router_instance = None


def get_intelligent_log_router() -> IntelligentLogRouter:
    """
    获取全局智能日志路由器实例
    
    Returns:
        IntelligentLogRouter: 路由器实例
    """
    global _router_instance
    if _router_instance is None:
        _router_instance = IntelligentLogRouter()
    return _router_instance
