#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能基准测试模块

复用现有的性能监控工具，对分布式日志系统进行性能基准测试
确保新系统性能优于现有系统
"""

import os
import sys
import time
import threading
import multiprocessing
import statistics
from typing import Dict, List, Optional, Any, Callable
from collections import defaultdict
from dataclasses import dataclass
import logging
import psutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入现有的模块
from utils.logger.manager import setup_unified_logging, get_unified_logger
from utils.logger.distributed_log_manager import setup_distributed_logging, get_enhanced_logger
from utils.logger.log_aggregator import LogEntry
from utils.logger.process_detector import get_process_detector


@dataclass
class BenchmarkResult:
    """基准测试结果数据类"""
    test_name: str
    system_type: str
    total_logs: int
    duration: float
    throughput: float
    avg_latency: float
    memory_usage: float
    cpu_usage: float
    success_rate: float
    errors: List[str]


@dataclass
class ComparisonResult:
    """比较结果数据类"""
    test_name: str
    original_result: BenchmarkResult
    distributed_result: BenchmarkResult
    throughput_improvement: float
    latency_improvement: float
    memory_efficiency: float
    cpu_efficiency: float
    overall_score: float


class PerformanceBenchmark:
    """
    性能基准测试
    
    复用现有的性能监控工具，对分布式日志系统进行全面的性能测试
    """
    
    def __init__(self):
        """初始化性能基准测试"""
        self.process_detector = get_process_detector()
        self.test_results = []
        self.comparison_results = []
        
        # 测试配置
        self.test_configs = {
            'single_thread': {
                'threads': 1,
                'logs_per_thread': 1000,
                'description': '单线程日志写入测试'
            },
            'multi_thread': {
                'threads': 4,
                'logs_per_thread': 500,
                'description': '多线程日志写入测试'
            },
            'high_volume': {
                'threads': 8,
                'logs_per_thread': 1000,
                'description': '高并发日志写入测试'
            },
            'mixed_levels': {
                'threads': 2,
                'logs_per_thread': 500,
                'description': '混合日志级别测试'
            },
            'long_messages': {
                'threads': 2,
                'logs_per_thread': 200,
                'description': '长消息日志测试'
            }
        }
    
    def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """
        运行综合基准测试
        
        Returns:
            Dict[str, Any]: 测试结果摘要
        """
        print("开始运行综合性能基准测试...")
        
        # 清理之前的结果
        self.test_results.clear()
        self.comparison_results.clear()
        
        # 运行所有测试配置
        for test_name, config in self.test_configs.items():
            print(f"\n运行测试: {config['description']}")
            
            # 测试原有系统
            original_result = self._test_original_system(test_name, config)
            
            # 测试分布式系统
            distributed_result = self._test_distributed_system(test_name, config)
            
            # 比较结果
            comparison = self._compare_results(test_name, original_result, distributed_result)
            
            self.test_results.extend([original_result, distributed_result])
            self.comparison_results.append(comparison)
            
            # 输出测试结果
            self._print_test_result(comparison)
        
        # 生成综合报告
        summary = self._generate_summary()
        self._print_summary(summary)
        
        return summary
    
    def _test_original_system(self, test_name: str, config: Dict[str, Any]) -> BenchmarkResult:
        """
        测试原有日志系统
        
        Args:
            test_name: 测试名称
            config: 测试配置
            
        Returns:
            BenchmarkResult: 测试结果
        """
        # 设置原有系统
        setup_unified_logging(log_level='debug')
        
        # 获取系统资源基线
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        baseline_cpu = process.cpu_percent()
        
        # 执行测试
        start_time = time.time()
        errors = []
        successful_logs = 0
        
        try:
            if config['threads'] == 1:
                # 单线程测试
                successful_logs = self._single_thread_test(
                    get_unified_logger, config['logs_per_thread'], test_name
                )
            else:
                # 多线程测试
                successful_logs = self._multi_thread_test(
                    get_unified_logger, config['threads'], 
                    config['logs_per_thread'], test_name
                )
        except Exception as e:
            errors.append(str(e))
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 获取资源使用情况
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = process.cpu_percent()
        
        # 计算指标
        total_logs = config['threads'] * config['logs_per_thread']
        throughput = successful_logs / duration if duration > 0 else 0
        avg_latency = (duration / successful_logs * 1000) if successful_logs > 0 else 0  # ms
        memory_usage = final_memory - baseline_memory
        cpu_usage = final_cpu - baseline_cpu
        success_rate = (successful_logs / total_logs * 100) if total_logs > 0 else 0
        
        return BenchmarkResult(
            test_name=test_name,
            system_type="original",
            total_logs=total_logs,
            duration=duration,
            throughput=throughput,
            avg_latency=avg_latency,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            success_rate=success_rate,
            errors=errors
        )
    
    def _test_distributed_system(self, test_name: str, config: Dict[str, Any]) -> BenchmarkResult:
        """
        测试分布式日志系统
        
        Args:
            test_name: 测试名称
            config: 测试配置
            
        Returns:
            BenchmarkResult: 测试结果
        """
        # 设置分布式系统
        setup_distributed_logging(
            enable_deduplication=True,
            enable_process_awareness=True,
            enable_aggregation=True,
            log_level='debug'
        )
        
        # 获取系统资源基线
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        baseline_cpu = process.cpu_percent()
        
        # 执行测试
        start_time = time.time()
        errors = []
        successful_logs = 0
        
        try:
            if config['threads'] == 1:
                # 单线程测试
                successful_logs = self._single_thread_test(
                    get_enhanced_logger, config['logs_per_thread'], test_name
                )
            else:
                # 多线程测试
                successful_logs = self._multi_thread_test(
                    get_enhanced_logger, config['threads'], 
                    config['logs_per_thread'], test_name
                )
        except Exception as e:
            errors.append(str(e))
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 获取资源使用情况
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = process.cpu_percent()
        
        # 计算指标
        total_logs = config['threads'] * config['logs_per_thread']
        throughput = successful_logs / duration if duration > 0 else 0
        avg_latency = (duration / successful_logs * 1000) if successful_logs > 0 else 0  # ms
        memory_usage = final_memory - baseline_memory
        cpu_usage = final_cpu - baseline_cpu
        success_rate = (successful_logs / total_logs * 100) if total_logs > 0 else 0
        
        return BenchmarkResult(
            test_name=test_name,
            system_type="distributed",
            total_logs=total_logs,
            duration=duration,
            throughput=throughput,
            avg_latency=avg_latency,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            success_rate=success_rate,
            errors=errors
        )
    
    def _single_thread_test(self, logger_func: Callable, log_count: int, test_name: str) -> int:
        """
        单线程测试
        
        Args:
            logger_func: 日志记录器获取函数
            log_count: 日志数量
            test_name: 测试名称
            
        Returns:
            int: 成功记录的日志数量
        """
        logger = logger_func(f'benchmark_{test_name}')
        successful_logs = 0
        
        for i in range(log_count):
            try:
                if test_name == 'mixed_levels':
                    # 混合级别测试
                    level = [logging.DEBUG, logging.INFO, logging.WARNING, logging.ERROR][i % 4]
                    logger.log(level, f"测试消息 {i} - 级别 {logging.getLevelName(level)}")
                elif test_name == 'long_messages':
                    # 长消息测试
                    long_message = f"这是一个很长的测试消息 {i} - " + "x" * 200
                    logger.info(long_message)
                else:
                    # 标准测试
                    logger.info(f"基准测试消息 {i} - 测试类型: {test_name}")
                
                successful_logs += 1
                
            except Exception:
                pass  # 忽略单个日志失败
        
        return successful_logs
    
    def _multi_thread_test(self, logger_func: Callable, thread_count: int, 
                          logs_per_thread: int, test_name: str) -> int:
        """
        多线程测试
        
        Args:
            logger_func: 日志记录器获取函数
            thread_count: 线程数量
            logs_per_thread: 每线程日志数量
            test_name: 测试名称
            
        Returns:
            int: 成功记录的日志数量
        """
        successful_logs = 0
        threads = []
        results = []
        
        def thread_worker(thread_id: int):
            """线程工作函数"""
            thread_successful = self._single_thread_test(
                logger_func, logs_per_thread, f"{test_name}_thread_{thread_id}"
            )
            results.append(thread_successful)
        
        # 启动线程
        for i in range(thread_count):
            thread = threading.Thread(target=thread_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待线程完成
        for thread in threads:
            thread.join()
        
        # 汇总结果
        successful_logs = sum(results)
        
        return successful_logs
    
    def _compare_results(self, test_name: str, original: BenchmarkResult, 
                        distributed: BenchmarkResult) -> ComparisonResult:
        """
        比较测试结果
        
        Args:
            test_name: 测试名称
            original: 原有系统结果
            distributed: 分布式系统结果
            
        Returns:
            ComparisonResult: 比较结果
        """
        # 计算改进百分比
        throughput_improvement = (
            (distributed.throughput - original.throughput) / max(1, original.throughput) * 100
        )
        
        latency_improvement = (
            (original.avg_latency - distributed.avg_latency) / max(1, original.avg_latency) * 100
        )
        
        memory_efficiency = (
            (original.memory_usage - distributed.memory_usage) / max(1, original.memory_usage) * 100
        )
        
        cpu_efficiency = (
            (original.cpu_usage - distributed.cpu_usage) / max(1, original.cpu_usage) * 100
        )
        
        # 计算综合评分
        overall_score = (
            throughput_improvement * 0.4 +
            latency_improvement * 0.3 +
            memory_efficiency * 0.15 +
            cpu_efficiency * 0.15
        )
        
        return ComparisonResult(
            test_name=test_name,
            original_result=original,
            distributed_result=distributed,
            throughput_improvement=throughput_improvement,
            latency_improvement=latency_improvement,
            memory_efficiency=memory_efficiency,
            cpu_efficiency=cpu_efficiency,
            overall_score=overall_score
        )
    
    def _print_test_result(self, comparison: ComparisonResult):
        """
        打印测试结果
        
        Args:
            comparison: 比较结果
        """
        print(f"\n{'='*60}")
        print(f"测试: {comparison.test_name}")
        print(f"{'='*60}")
        
        print(f"吞吐量改进: {comparison.throughput_improvement:+.1f}%")
        print(f"延迟改进: {comparison.latency_improvement:+.1f}%")
        print(f"内存效率: {comparison.memory_efficiency:+.1f}%")
        print(f"CPU效率: {comparison.cpu_efficiency:+.1f}%")
        print(f"综合评分: {comparison.overall_score:+.1f}")
        
        print(f"\n详细对比:")
        print(f"  原有系统 - 吞吐量: {comparison.original_result.throughput:.1f} logs/s, "
              f"延迟: {comparison.original_result.avg_latency:.2f}ms")
        print(f"  分布式系统 - 吞吐量: {comparison.distributed_result.throughput:.1f} logs/s, "
              f"延迟: {comparison.distributed_result.avg_latency:.2f}ms")
    
    def _generate_summary(self) -> Dict[str, Any]:
        """
        生成测试摘要
        
        Returns:
            Dict[str, Any]: 测试摘要
        """
        if not self.comparison_results:
            return {"message": "没有测试结果"}
        
        # 计算平均改进
        avg_throughput_improvement = statistics.mean(
            [r.throughput_improvement for r in self.comparison_results]
        )
        avg_latency_improvement = statistics.mean(
            [r.latency_improvement for r in self.comparison_results]
        )
        avg_memory_efficiency = statistics.mean(
            [r.memory_efficiency for r in self.comparison_results]
        )
        avg_cpu_efficiency = statistics.mean(
            [r.cpu_efficiency for r in self.comparison_results]
        )
        avg_overall_score = statistics.mean(
            [r.overall_score for r in self.comparison_results]
        )
        
        # 找出最佳和最差测试
        best_test = max(self.comparison_results, key=lambda x: x.overall_score)
        worst_test = min(self.comparison_results, key=lambda x: x.overall_score)
        
        # 成功率统计
        original_success_rates = [r.original_result.success_rate for r in self.comparison_results]
        distributed_success_rates = [r.distributed_result.success_rate for r in self.comparison_results]
        
        return {
            'test_count': len(self.comparison_results),
            'average_improvements': {
                'throughput': avg_throughput_improvement,
                'latency': avg_latency_improvement,
                'memory_efficiency': avg_memory_efficiency,
                'cpu_efficiency': avg_cpu_efficiency,
                'overall_score': avg_overall_score
            },
            'best_test': {
                'name': best_test.test_name,
                'score': best_test.overall_score
            },
            'worst_test': {
                'name': worst_test.test_name,
                'score': worst_test.overall_score
            },
            'success_rates': {
                'original_avg': statistics.mean(original_success_rates),
                'distributed_avg': statistics.mean(distributed_success_rates)
            },
            'recommendation': self._generate_recommendation(avg_overall_score)
        }
    
    def _generate_recommendation(self, overall_score: float) -> str:
        """
        生成建议
        
        Args:
            overall_score: 综合评分
            
        Returns:
            str: 建议
        """
        if overall_score > 20:
            return "强烈推荐迁移到分布式日志系统，性能显著提升"
        elif overall_score > 10:
            return "推荐迁移到分布式日志系统，性能有所提升"
        elif overall_score > 0:
            return "可以考虑迁移到分布式日志系统，性能略有提升"
        elif overall_score > -10:
            return "分布式日志系统性能与原有系统相当，可根据功能需求决定"
        else:
            return "暂不推荐迁移，需要进一步优化分布式日志系统"
    
    def _print_summary(self, summary: Dict[str, Any]):
        """
        打印测试摘要
        
        Args:
            summary: 测试摘要
        """
        print(f"\n{'='*80}")
        print(f"性能基准测试摘要")
        print(f"{'='*80}")
        
        improvements = summary['average_improvements']
        print(f"平均性能改进:")
        print(f"  吞吐量: {improvements['throughput']:+.1f}%")
        print(f"  延迟: {improvements['latency']:+.1f}%")
        print(f"  内存效率: {improvements['memory_efficiency']:+.1f}%")
        print(f"  CPU效率: {improvements['cpu_efficiency']:+.1f}%")
        print(f"  综合评分: {improvements['overall_score']:+.1f}")
        
        print(f"\n最佳测试: {summary['best_test']['name']} (评分: {summary['best_test']['score']:+.1f})")
        print(f"最差测试: {summary['worst_test']['name']} (评分: {summary['worst_test']['score']:+.1f})")
        
        success_rates = summary['success_rates']
        print(f"\n成功率:")
        print(f"  原有系统: {success_rates['original_avg']:.1f}%")
        print(f"  分布式系统: {success_rates['distributed_avg']:.1f}%")
        
        print(f"\n建议: {summary['recommendation']}")
    
    def export_results(self, filename: str = None) -> str:
        """
        导出测试结果
        
        Args:
            filename: 文件名
            
        Returns:
            str: 导出的文件路径
        """
        if filename is None:
            filename = f"benchmark_results_{int(time.time())}.json"
        
        import json
        
        export_data = {
            'timestamp': time.time(),
            'test_results': [
                {
                    'test_name': r.test_name,
                    'system_type': r.system_type,
                    'total_logs': r.total_logs,
                    'duration': r.duration,
                    'throughput': r.throughput,
                    'avg_latency': r.avg_latency,
                    'memory_usage': r.memory_usage,
                    'cpu_usage': r.cpu_usage,
                    'success_rate': r.success_rate,
                    'errors': r.errors
                }
                for r in self.test_results
            ],
            'comparison_results': [
                {
                    'test_name': c.test_name,
                    'throughput_improvement': c.throughput_improvement,
                    'latency_improvement': c.latency_improvement,
                    'memory_efficiency': c.memory_efficiency,
                    'cpu_efficiency': c.cpu_efficiency,
                    'overall_score': c.overall_score
                }
                for c in self.comparison_results
            ],
            'summary': self._generate_summary()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return filename


# 全局基准测试实例
_benchmark_instance = None


def get_performance_benchmark() -> PerformanceBenchmark:
    """
    获取全局性能基准测试实例
    
    Returns:
        PerformanceBenchmark: 基准测试实例
    """
    global _benchmark_instance
    if _benchmark_instance is None:
        _benchmark_instance = PerformanceBenchmark()
    return _benchmark_instance


def run_quick_benchmark() -> Dict[str, Any]:
    """
    运行快速基准测试
    
    Returns:
        Dict[str, Any]: 测试结果摘要
    """
    benchmark = get_performance_benchmark()
    
    # 只运行单线程和多线程测试
    quick_configs = {
        'single_thread': benchmark.test_configs['single_thread'],
        'multi_thread': benchmark.test_configs['multi_thread']
    }
    
    original_configs = benchmark.test_configs
    benchmark.test_configs = quick_configs
    
    try:
        result = benchmark.run_comprehensive_benchmark()
        return result
    finally:
        benchmark.test_configs = original_configs
