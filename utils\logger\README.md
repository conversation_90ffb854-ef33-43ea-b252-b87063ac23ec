# 分布式日志管理系统

这是一个先进的分布式日志管理系统，专为多进程环境设计，提供智能去重、进程感知、日志聚合和性能优化功能。

## 🚀 核心特性

### 分布式架构
- **多进程支持**: 专为多进程环境优化，解决进程间日志冲突问题
- **智能去重**: 自动检测和过滤重复日志，显著减少日志冗余
- **进程感知**: 根据进程类型（主进程、工作进程、子进程）智能调整日志策略
- **日志聚合**: 高效的日志收集和批处理机制，提升性能

### 智能功能
- **智能路由**: 根据日志内容、级别和进程类型智能路由到不同输出
- **动态配置**: 支持运行时动态调整日志配置
- **性能监控**: 实时监控日志系统性能和资源使用
- **质量分析**: 自动分析日志质量并生成详细报告

### 兼容性
- **向后兼容**: 与现有日志系统完全兼容，无需修改现有代码
- **渐进式迁移**: 支持平滑迁移，可以逐步启用新功能
- **回退机制**: 提供安全的回退机制，确保系统稳定性

## 🎯 快速开始

### 方式一：兼容模式（推荐新用户）

```python
from utils.logger.manager import setup_unified_logging, get_unified_logger

# 设置日志系统（自动启用分布式功能）
setup_unified_logging(log_level='info')

# 获取日志记录器
logger = get_unified_logger('my_module')

# 记录日志
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
```

### 方式二：分布式模式（推荐高级用户）

```python
from utils.logger.distributed_log_manager import setup_distributed_logging, get_enhanced_logger

# 设置分布式日志系统
setup_distributed_logging(
    enable_deduplication=True,      # 启用智能去重
    enable_process_awareness=True,  # 启用进程感知
    enable_aggregation=True,        # 启用日志聚合
    log_level='info'
)

# 获取增强的日志记录器
logger = get_enhanced_logger('my_module')

# 记录日志（自动应用智能过滤和路由）
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
```

## 📊 系统架构

### 核心组件

1. **进程检测器** (`process_detector.py`)
   - 自动检测进程类型和角色
   - 支持主进程、工作进程、子进程识别
   - 提供进程特定的配置建议

2. **日志聚合器** (`log_aggregator.py`)
   - 高效的日志收集和批处理
   - 支持多进程环境下的日志聚合
   - 提供实时统计和监控

3. **智能去重器** (`log_deduplicator.py`)
   - 基于内容和时间窗口的去重算法
   - 支持相似度检测和模式匹配
   - 可配置的去重策略

4. **智能路由器** (`intelligent_log_router.py`)
   - 基于规则的日志路由
   - 支持动态路由策略
   - 提供缓冲和批处理机制

5. **分布式管理器** (`distributed_log_manager.py`)
   - 统一的分布式日志管理
   - 集成所有核心组件
   - 提供简单易用的API

### 数据流

```
日志记录 → 进程检测 → 智能过滤 → 去重处理 → 智能路由 → 输出目标
    ↓           ↓           ↓           ↓           ↓
  原始日志   进程信息   过滤结果   去重结果   路由决策
```

## ⚙️ 配置选项

### 环境变量配置

```bash
# 分布式日志系统开关
export ENABLE_DISTRIBUTED_LOGGING=true

# 去重功能开关
export ENABLE_LOG_DEDUPLICATION=true

# 进程感知开关
export ENABLE_PROCESS_AWARE_LOGGING=true

# 进程特定日志级别
export MAIN_PROCESS_LOG_LEVEL=DEBUG
export WORKER_PROCESS_LOG_LEVEL=WARNING
export CHILD_PROCESS_LOG_LEVEL=INFO

# 聚合器配置
export LOG_AGGREGATOR_QUEUE_SIZE=10000
export LOG_AGGREGATOR_BATCH_SIZE=100

# 去重器配置
export LOG_DEDUP_CACHE_SIZE=10000
export LOG_DEDUP_TIME_WINDOW=60.0
export LOG_DEDUP_SIMILARITY_THRESHOLD=0.8

# 工作进程日志抑制
export SUPPRESS_WORKER_INIT_LOGS=true
export SUPPRESS_WORKER_DEBUG_LOGS=true
export MAX_INIT_LOGS_PER_MODULE=1
```

### 代码配置

```python
from utils.logger.distributed_log_manager import setup_distributed_logging

setup_distributed_logging(
    # 基本配置
    enable_deduplication=True,
    enable_process_awareness=True,
    enable_aggregation=True,
    log_level='info',

    # 高级配置
    aggregator_config={
        'max_queue_size': 10000,
        'batch_size': 100,
        'enable_stats': True
    },
    deduplicator_config={
        'cache_size': 10000,
        'time_window': 60.0,
        'similarity_threshold': 0.8
    }
)
```

## 🔧 高级功能

### 1. 日志质量分析

```python
from utils.logger.log_analysis_engine import get_log_analysis_engine

# 获取分析引擎
engine = get_log_analysis_engine()

# 执行综合分析
result = engine.analyze_logs("comprehensive")
print(f"日志质量评分: {result.severity_score}")
print(f"建议: {result.recommendations}")
```

### 2. 性能基准测试

```python
from utils.logger.performance_benchmark import run_quick_benchmark

# 运行快速基准测试
results = run_quick_benchmark()
print(f"性能改进: {results['average_improvements']['overall_score']:.1f}%")
```

### 3. 系统健康报告

```python
from utils.logger.log_statistics_reporter import get_log_statistics_reporter

# 获取统计报告器
reporter = get_log_statistics_reporter()

# 生成健康报告
report = reporter.generate_quality_report()
print(f"系统健康评分: {report.overall_health_score}/100")

# 导出为JSON
json_report = reporter.export_report_to_json(report)
```

### 4. 兼容性迁移

```python
from utils.logger.compatibility_adapter import (
    migrate_to_distributed_logging,
    get_migration_status,
    test_logging_compatibility
)

# 启用渐进式迁移
migrate_to_distributed_logging(
    enable_warnings=True,
    enable_fallback=True
)

# 检查迁移状态
status = get_migration_status()
print(f"新系统使用率: {status['new_system_usage_rate']:.1f}%")

# 测试兼容性
test_results = test_logging_compatibility()
print(f"兼容性测试: {'通过' if all(test_results.values()) else '失败'}")
```

## 🛠️ 维护工具

### 配置验证工具

```python
from utils.logger.config_validation_tool import (
    validate_logging_config,
    print_validation_report
)

# 验证配置
report = validate_logging_config()
print(f"配置健康评分: {report['health_score']}/100")

# 打印详细报告
print_validation_report()
```

### 系统诊断

```python
from utils.logger.distributed_log_manager import get_distributed_log_manager

# 获取系统统计
manager = get_distributed_log_manager()
stats = manager.get_system_stats()

print(f"系统状态: {'运行中' if stats['is_setup'] else '未启动'}")
print(f"进程信息: {stats['process_info']}")
```

## 📈 性能优化

### 推荐配置

根据不同使用场景，推荐以下配置：

#### 高性能场景
```python
setup_distributed_logging(
    enable_deduplication=True,
    enable_process_awareness=True,
    enable_aggregation=True,
    aggregator_config={
        'max_queue_size': 50000,
        'batch_size': 500
    },
    deduplicator_config={
        'cache_size': 20000,
        'time_window': 30.0
    }
)
```

#### 内存优化场景
```python
setup_distributed_logging(
    enable_deduplication=True,
    enable_process_awareness=True,
    enable_aggregation=True,
    aggregator_config={
        'max_queue_size': 5000,
        'batch_size': 50
    },
    deduplicator_config={
        'cache_size': 2000,
        'time_window': 120.0
    }
)
```

#### 开发调试场景
```python
setup_distributed_logging(
    enable_deduplication=False,  # 保留所有日志
    enable_process_awareness=True,
    enable_aggregation=False,    # 实时输出
    log_level='debug'
)
```

## 🔍 故障排除

### 常见问题

1. **重复日志过多**
   ```python
   # 启用去重功能
   setup_distributed_logging(enable_deduplication=True)
   ```

2. **工作进程日志过多**
   ```python
   # 调整工作进程日志级别
   os.environ['WORKER_PROCESS_LOG_LEVEL'] = 'WARNING'
   ```

3. **性能问题**
   ```python
   # 增加批处理大小
   setup_distributed_logging(
       aggregator_config={'batch_size': 200}
   )
   ```

4. **内存使用过高**
   ```python
   # 减少缓存大小
   setup_distributed_logging(
       deduplicator_config={'cache_size': 5000}
   )
   ```

### 诊断命令

```bash
# 验证配置
python -m utils.logger.config_validation_tool

# 运行性能测试
python -c "from utils.logger.performance_benchmark import run_quick_benchmark; run_quick_benchmark()"

# 检查兼容性
python -c "from utils.logger.compatibility_adapter import test_logging_compatibility; print(test_logging_compatibility())"
```

## 📚 API 参考

### 主要模块

- `distributed_log_manager.py`: 分布式日志管理器
- `log_aggregator.py`: 日志聚合器
- `log_deduplicator.py`: 日志去重器
- `intelligent_log_router.py`: 智能日志路由器
- `process_detector.py`: 进程检测器
- `log_analysis_engine.py`: 日志分析引擎
- `log_statistics_reporter.py`: 统计报告器
- `performance_benchmark.py`: 性能基准测试
- `compatibility_adapter.py`: 兼容性适配器
- `config_validation_tool.py`: 配置验证工具

### 配置参数

详细的配置参数说明请参考 `config/settings.py` 中的分布式日志配置部分。

## 🤝 贡献指南

1. 遵循现有的代码风格和架构模式
2. 添加适当的单元测试
3. 更新相关文档
4. 确保向后兼容性
5. 运行性能基准测试验证改进

## 📄 许可证

本项目遵循项目根目录下的许可证文件。

## 最近优化

日志系统最近进行了一系列优化，以提高代码质量和减少冗余：

1. **格式化器优化**：移除了旧的格式化器，只保留EnhancedFormatter和TableMarkdownFormatter，减少了代码冗余。
2. **日志目标表示统一**：推荐使用LogTarget枚举（LogTarget.FILE/CONSOLE/BOTH）代替数字代码（1/2/3）来指定日志输出目标，提高代码可读性。
3. **日志前置内容功能**：增加了为日志添加自定义前置内容的功能，可以在日志前添加分隔线、标题或换行符等，使用户可以完全控制日志的显示格式。
4. **移除自动分隔线**：移除了自动分隔线功能，改为由用户通过前置内容参数自行控制。
5. **日志解析增强**：日志查看工具现在支持多种日志格式，减少了对特定格式的依赖，提高了兼容性。
6. **代码清理**：移除了未使用的代码，添加了更详细的文档和警告，便于开发者正确使用API。

## 快速开始

### 基本用法

```python
from utils.logger import get_unified_logger, LogTarget, setup_unified_logging

# 初始化日志系统（默认日志只输出到文件）
setup_unified_logging()

# 获取日志记录器
logger = get_unified_logger("my_module")

# 使用目标代码记录日志
logger.info(LogTarget.FILE, "这条信息只记录到文件")
logger.info(LogTarget.CONSOLE, "这条信息只显示在控制台")
logger.info(LogTarget.BOTH, "这条信息同时记录到文件和显示在控制台")

# 使用前置内容
logger.info(LogTarget.FILE, "=======================", "这条信息前面会有分隔线")
logger.info(LogTarget.FILE, "\n\n\n", "这条信息前面会有多个换行符")
logger.info(LogTarget.FILE, "=====================数据下载=====================", "这是数据下载部分的日志")

# 使用标题和正文分离功能（新增）
logger.info(title_target=LogTarget.BOTH, title="【数据下载开始】", 
            content_target=LogTarget.FILE, content="下载详细数据...")

# 只设置标题，不设置正文
logger.info(title_target=LogTarget.BOTH, title="【操作完成】")

# 只设置正文，不设置标题
logger.info(content_target=LogTarget.FILE, content="详细的操作日志...")

# 使用标准日志级别方法
logger.debug(LogTarget.FILE, "这条调试信息只记录到文件")
logger.warning(LogTarget.CONSOLE, "这条警告只显示在控制台")
logger.error(LogTarget.BOTH, "这条错误同时记录到文件和显示在控制台")

# 默认行为（只输出到文件）
logger.debug("这条调试信息默认只记录到文件")
logger.info("这条信息默认只记录到文件")
```

### 设置统一日志系统

```python
from utils.logger import setup_unified_logging, LogTarget

# 设置统一的日志系统
setup_unified_logging()

# 可以自定义配置
setup_unified_logging(
    logger_names=["my_module", "another_module"],
    log_level="debug",
    task_id="CUSTOM1",
    clean_old_logs=True,
    days_to_keep=14
)

# 配置默认日志输出目标为只输出到文件（减少终端输出）
setup_unified_logging(default_target=LogTarget.FILE)

# 配置默认日志输出目标为只输出到控制台（不写入文件）
setup_unified_logging(default_target=LogTarget.CONSOLE)

# 配置默认日志输出目标为同时输出到文件和控制台
setup_unified_logging(default_target=LogTarget.BOTH)

# 控制是否打印初始化消息（默认为False）
setup_unified_logging(
    default_target=LogTarget.FILE,
    print_init_message=True  # 打印初始化完成消息
)
```

## 高级功能

### 自定义过滤器

```python
from utils.logger import TargetFilter, LogTarget
import logging

# 创建一个只处理文件目标日志的处理器
file_handler = logging.FileHandler("app.log")
file_filter = TargetFilter(LogTarget.FILE)
file_handler.addFilter(file_filter)
```

### 统一日志管理

```python
from utils.logger import setup_unified_logging, get_unified_logger

# 初始化统一日志系统
setup_unified_logging()

# 获取统一配置的日志记录器
logger = get_unified_logger("my_module")
logger.info(LogTarget.BOTH, "记录日志")

# 清理旧日志文件
from utils.logger import clean_old_log_files
deleted_count = clean_old_log_files(days_to_keep=30)
print(f"已删除 {deleted_count} 个旧日志文件")
```

### 使用标题和正文分离功能

增强型日志系统支持分别控制标题和正文的输出位置，这对于需要在控制台显示简洁信息但在日志文件中记录详细内容的场景特别有用：

```python
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger("my_module")

# 标题显示在控制台和文件中，正文只记录到文件
logger.info(
    title_target=LogTarget.BOTH, 
    title="【当前进度：显示保存数据】", 
    content_target=LogTarget.FILE, 
    content="详细的数据内容...\n数据行1\n数据行2\n..."
)

# 标题只显示在控制台，正文只记录到文件
logger.info(
    title_target=LogTarget.CONSOLE, 
    title="【操作提示】数据处理中，请稍候...", 
    content_target=LogTarget.FILE, 
    content="正在处理数据，当前进度：25%"
)

# 标题和正文都显示在控制台和文件中，但内容不同
logger.info(
    title_target=LogTarget.BOTH, 
    title="【操作完成】数据下载成功", 
    content_target=LogTarget.BOTH, 
    content="共下载了1000条记录，耗时5.2秒"
)

# 只设置标题，不设置正文
logger.info(title_target=LogTarget.BOTH, title="【分隔线】" + "=" * 50)

# 只设置正文，不设置标题
logger.info(content_target=LogTarget.FILE, content="这是一条详细的日志记录...")
```

#### 实际应用场景

标题和正文分离功能在以下场景特别有用：

1. **数据展示**：在控制台显示简洁的标题，同时在日志文件中记录完整的数据表格
   ```python
   logger.info(
       title_target=LogTarget.BOTH, 
       title=f"\n【当前进度：显示保存数据】{symbol} - {period} (数据头部{head_rows}行)",
       content_target=LogTarget.FILE, 
       content=f"{head_table}\n"
   )
   ```

2. **进度报告**：在控制台实时更新进度，同时在日志文件中记录详细信息
   ```python
   logger.info(
       title_target=LogTarget.CONSOLE, 
       title=f"处理进度: {progress}%", 
       content_target=LogTarget.FILE, 
       content=f"详细处理状态: 已处理{processed_count}/{total_count}项，当前处理: {current_item}"
   )
   ```

3. **错误报告**：在控制台显示简洁的错误提示，同时在日志文件中记录详细的错误信息和堆栈跟踪
   ```python
   logger.error(
       title_target=LogTarget.BOTH, 
       title=f"处理{file_name}时发生错误: {e}", 
       content_target=LogTarget.FILE, 
       content=f"详细错误信息:\n{traceback.format_exc()}"
   )
   ```

### 新增：纯关键字参数调用方式

从 v2.3.0 版本开始，增强型日志系统支持纯关键字参数调用方式，无需提供位置参数，使代码更加清晰可读：

```python
# 旧方式 - 必须提供位置参数 target_or_msg
logger.info(LogTarget.BOTH, "这条日志同时输出到文件和控制台")

# 新方式 - 使用纯关键字参数，不需要位置参数
logger.info(title_target=LogTarget.BOTH, title="这条日志同时输出到文件和控制台")
logger.info(content_target=LogTarget.FILE, content="这条日志只记录到文件")

# 同时使用标题和正文
logger.info(
    title_target=LogTarget.CONSOLE,
    title="【控制台标题】进度更新",
    content_target=LogTarget.FILE,
    content="详细进度数据..."
)
```

#### 纯关键字参数调用的优势

1. **更清晰的代码**：明确指定每个参数的作用，提高代码可读性
2. **更灵活的控制**：可以只指定需要的参数，其他参数使用默认值
3. **减少错误**：避免位置参数顺序错误导致的问题
4. **更好的IDE支持**：IDE可以提供更好的参数提示

#### 兼容性说明

新的纯关键字参数调用方式与旧的位置参数调用方式完全兼容，您可以根据喜好选择使用方式。为了代码一致性和可读性，建议在新代码中使用纯关键字参数调用方式。

## 格式化器

本模块提供了几种不同的日志格式化器：

1. **默认格式化器**：使用标准格式输出日志
2. **增强型格式化器**（推荐使用）：提供更多高级功能，包括：
   - 根据日志级别添加颜色
   - 根据模块名称添加颜色
   - 支持用户自定义前置内容
   - 为相关日志添加缩进，表示层次关系
   - 突出显示重要信息
3. **表格式Markdown格式化器**：使用表格式布局，各字段用竖线(|)分隔，提供更清晰的日志格式

可以通过`get_formatter`函数获取不同类型的格式化器：

```python
from utils.logger.formatters import get_formatter

# 获取默认格式化器
default_formatter = get_formatter('default')

# 获取增强型格式化器 (推荐)
enhanced_formatter = get_formatter(
    'enhanced', 
    task_id='TASK123',
    use_colors=True,
    use_icons=True,
    module_width=30,
    function_width=30
)

# 获取表格式Markdown格式化器
table_formatter = get_formatter(
    'table_markdown',
    task_id='TASK123',
    use_colors=True,
    module_width=30,
    function_width=30
)
```

## 使用增强型日志系统

通过设置`use_enhanced_formatter=True`可以启用增强型日志格式化器：

```python
from utils.logger import setup_unified_logging

# 启用增强型格式化器
setup_unified_logging(
    use_enhanced_formatter=True,
    use_colors=True,
    module_width=30,  # 设置模块名显示宽度
    function_width=30  # 设置函数名显示宽度
)
```

增强型格式化器的主要特点：

1. **前置内容**：允许用户为日志添加自定义前置内容，如分隔线、换行符或标题
2. **颜色标识**：根据日志级别和模块名称添加颜色，便于快速识别
3. **层次缩进**：自动为相关操作的日志添加缩进，表示层次关系
4. **操作分组**：自动识别操作的开始和结束，将相关日志分组显示
5. **函数级别日志**：自动检测并显示调用日志的实际函数名，格式为"模块名【函数名】"

### 使用日志前置内容

增强型日志系统支持为日志添加自定义前置内容，这对于创建视觉上的分隔或组织日志很有用：

```python
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger("my_module")

# 添加简单分隔线
logger.info(LogTarget.FILE, "========================================", "开始处理数据")

# 添加标题分隔线
logger.info(LogTarget.FILE, "======================数据下载======================", "开始下载数据")

# 添加多个换行符，在日志中创建空白区域
logger.info(LogTarget.FILE, "\n\n\n\n\n", "这条日志前面有多个空行")

# 混合内容
logger.info(LogTarget.FILE, "\n\n========================================\n", "新的处理阶段")
```

#### 常见的前置内容模式

以下是一些常见的前置内容模式，可根据需要自定义：

```python
# 简单分隔线
prefix_separator = "=" * 80

# 带标题的分隔线
def title_separator(title):
    line = "=" * 80
    start_pos = (len(line) - len(title)) // 2 - 1
    if start_pos < 0:
        start_pos = 0
    return line[:start_pos] + " " + title + " " + line[start_pos + len(title) + 2:]

# 多行空白
prefix_blank_lines = "\n" * 10

# 使用示例
logger.info(LogTarget.FILE, prefix_separator, "开始新的处理阶段")
logger.info(LogTarget.FILE, title_separator("数据处理"), "开始处理数据")
logger.info(LogTarget.FILE, prefix_blank_lines, "在日志中创建一个视觉分隔区")
```

## API参考

### 核心函数

- `get_unified_logger(name, level="info", enhanced=True, ...)`: 获取统一配置的日志记录器（推荐）
- `setup_unified_logging(...)`: 设置统一的日志系统
  - `default_target`: 默认日志输出目标，可以是`LogTarget.FILE`（只输出到文件，默认值）、`LogTarget.CONSOLE`（只输出到控制台）或`LogTarget.BOTH`（两者都输出）
- `clean_old_log_files(days_to_keep=7)`: 清理指定天数之前的日志文件

### 常量

- `LogTarget.FILE`: 只输出到文件
- `LogTarget.CONSOLE`: 只输出到控制台
- `LogTarget.BOTH`: 同时输出到文件和控制台

### 过滤器

- `TargetFilter`: 根据目标类型过滤日志记录

## 最佳实践

1. **使用统一日志系统**：优先使用`setup_unified_logging`和`get_unified_logger`
2. **明确指定目标**：为每条日志明确指定输出目标，避免不必要的重复
3. **集中配置**：使用`setup_unified_logging`集中配置日志系统
4. **使用任务ID**：利用自动生成的任务ID关联相关日志，便于跟踪和调试
5. **适当使用过滤器**：使用过滤器控制日志流向，避免信息过载
6. **定期清理日志**：使用`clean_old_log_files`定期清理旧日志文件
7. **使用LogTarget枚举**：使用`LogTarget.FILE`/`LogTarget.CONSOLE`/`LogTarget.BOTH`代替数字代码
8. **使用EnhancedFormatter**：优先使用增强型格式化器，而非旧的格式化器
9. **分离标题和正文**：对于需要在控制台显示简洁信息但在日志文件中记录详细内容的场景，使用标题和正文分离功能
10. **利用函数级别日志**：利用自动显示的函数名信息，更容易追踪代码执行流程和定位问题

# 统一日志系统

本模块提供了一个统一的日志系统，用于整个项目的日志记录。

## 主要功能

- 统一的日志配置和管理
- 支持多种日志输出目标（控制台、文件、网络等）
- 支持日志级别控制
- 支持日志格式化
- 支持日志文件轮转
- 支持日志过滤
- 支持任务ID关联，便于跟踪相关日志
- 支持日志文件清理

## 主要组件

- `manager.py`: 日志管理器，提供统一的日志接口
- `handlers.py`: 自定义日志处理器（计划中）
- `formatters.py`: 自定义日志格式化器
- `filters.py`: 自定义日志过滤器
- `decorators.py`: 日志装饰器
- `config.py`: 日志配置
- `examples.py`: 日志系统示例和测试

## 使用方法

### 基本使用

```python
from utils.logger import get_unified_logger

# 获取日志记录器
logger = get_unified_logger(__name__)

# 记录日志
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
```

### 增强日志

```python
from utils.logger import get_unified_logger, LogTarget

# 获取增强日志记录器
logger = get_unified_logger(__name__, enhanced=True)

# 同时记录到控制台和文件
logger.info(LogTarget.BOTH, "这条日志会同时记录到控制台和文件")

# 只记录到文件
logger.debug(LogTarget.FILE, "这条日志只会记录到文件")

# 只记录到控制台
logger.warning(LogTarget.CONSOLE, "这条日志只会记录到控制台")
```

### 设置日志级别

```python
from utils.logger import setup_unified_logging

# 设置全局日志级别
setup_unified_logging(log_level="debug")
```

### 获取日志文件路径

```python
from utils.logger import get_log_file_path

# 获取特定模块的日志文件路径
log_path = get_log_file_path("my_module")
```

### 清理旧日志文件

```python
from utils.logger import clean_old_log_files

# 清理7天前的日志文件
clean_old_log_files(days_to_keep=7)
```

### 测试日志系统

可以通过以下方式测试日志系统是否正常工作：

1. 使用命令行参数：

```bash
# 在项目根目录下运行
python data/data_main.py --test-logging
```

2. 直接运行测试模块：

```bash
# 在项目根目录下运行
python utils/logger/examples.py --test
```

3. 在代码中调用测试函数：

```python
from utils.logger.examples import test_logging_system

# 运行日志系统测试
test_logging_system()
```

## 最近更新

- **统一化改进**: 整合了项目中的多个日志实现，确保所有模块使用统一的日志系统
  - 将直接使用 `logging.getLogger()` 的代码替换为 `get_unified_logger()`
  - 删除了旧的日志记录器实现文件和兼容层
  - 更新了 `config/logging_config.py` 作为简化的兼容层
- **格式化器优化**: 移除了旧的格式化器，只保留EnhancedFormatter和TableMarkdownFormatter
- **日志目标表示统一**: 推荐使用LogTarget枚举代替数字代码来指定日志输出目标
- **日志解析增强**: 日志查看工具现在支持多种日志格式，减少了对特定格式的依赖

## 最佳实践

1. 始终使用 `get_unified_logger()` 获取日志记录器
2. 为每个模块使用唯一的日志记录器名称（通常是 `__name__`）
3. 适当使用日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
4. 使用 `LogTarget` 枚举指定日志输出目标
5. 定期清理旧日志文件
6. 使用增强型格式化器（EnhancedFormatter）而非旧的格式化器
7. 使用LogTarget枚举：使用`LogTarget.FILE`/`LogTarget.CONSOLE`/`LogTarget.BOTH`代替数字代码
8. 使用EnhancedFormatter：优先使用增强型格式化器，而非旧的格式化器
9. 分离标题和正文：对于需要在控制台显示简洁信息但在日志文件中记录详细内容的场景，使用标题和正文分离功能
10. 利用函数级别日志：利用自动显示的函数名信息，更容易追踪代码执行流程和定位问题

## 日志查看工具

本模块提供了一个强大的日志查看工具，用于方便地查看、过滤、搜索和分析日志文件。

### 主要功能

- **多种显示模式**：支持平铺、树形和按操作分组三种查看模式
- **过滤功能**：可按时间、任务ID、日志级别、模块名称和消息内容进行过滤
- **彩色显示**：根据日志级别使用不同颜色显示，提高可读性
- **操作分组**：自动识别操作的开始和结束，对相关日志进行分组
- **详细模式**：可显示日志行号等额外信息，便于定位问题
- **导出功能**：支持导出为文本或HTML格式
- **摘要模式**：只显示关键信息，减少输出量
- **多格式支持**：支持多种日志格式，包括标准格式、无任务ID格式和简单格式

### 使用方法

#### 命令行使用

```bash
# 查看最新日志文件
python -m utils.logger.viewer

# 查看特定日志文件
python -m utils.logger.viewer --log-file=logs/quant_20250606.log

# 按操作分组查看
python -m utils.logger.viewer --view-mode=grouped

# 查看特定模块的日志
python -m utils.logger.viewer --module=data_source_manager

# 按树形结构查看
python -m utils.logger.viewer --view-mode=tree

# 导出日志为HTML
python -m utils.logger.viewer --export-to=log_report.html

# 搜索特定内容
python -m utils.logger.viewer --message="下载数据"

# 显示详细信息
python -m utils.logger.viewer --detailed
```

#### 可用参数

- `--log-file`: 指定要查看的日志文件路径
- `--start-time`: 过滤开始时间，格式：YYYY-MM-DD HH:MM:SS
- `--end-time`: 过滤结束时间，格式：YYYY-MM-DD HH:MM:SS
- `--task-id`: 过滤特定任务ID
- `--level`: 过滤特定日志级别
- `--module`: 过滤特定模块
- `--message`: 过滤包含特定内容的日志
- `--export-to`: 导出日志到指定文件
- `--no-color`: 禁用颜色显示
- `--operations`: 查看操作及其相关日志
- `--show-details`: 显示操作的详细日志
- `--group-by-operation`: 按操作分组显示日志
- `--detailed`: 显示详细信息，包括日志条目的行号
- `--summary`: 仅显示操作摘要，不显示详细日志
- `--view-mode`: 日志查看模式，可选：flat（平铺显示）、tree（树形显示）、grouped（按操作分组）

### 在代码中使用

```python
from utils.logger.viewer import view_logs, view_operations, LogViewer

# 查看最新日志
view_logs()

# 查看特定模块的日志
view_logs(module="data_source_manager")

# 查看操作及其相关日志
view_operations(show_details=True)

# 使用LogViewer类进行高级操作
viewer = LogViewer("logs/quant_20250606.log")
viewer.parse_log_file()

# 过滤日志
filtered_logs = viewer.filter_logs(level="INFO", module="data_source_manager")

# 搜索日志
search_results = viewer.search("下载完成")

# 导出为HTML
viewer.export_logs("log_report.html", filtered_logs, use_colors=True)
```

## API参考

### 日志查看工具函数

- `view_logs(...)`: 查看日志文件，支持多种过滤选项和导出功能
- `view_operations(...)`: 查看操作及其相关日志
- `get_latest_log_file(...)`: 获取最新的日志文件路径

## 最佳实践

1. **使用日志查看工具**：使用`utils.logger.viewer`模块查看和分析日志文件
2. **按操作分组查看**：使用`--view-mode=grouped`选项按操作分组查看日志，更容易理解流程
3. **导出为HTML**：使用`--export-to=file.html`选项导出日志为HTML格式，便于在浏览器中查看

### 新功能：标题前置显示

从 v2.4.0 版本开始，当使用标题和正文分离功能时，标题会显示在日志头部之前，使其更加突出和易于识别。这对于需要在大量日志中快速定位重要信息的场景特别有用。

示例输出格式：
```
【当前进度：标准化需要下载的股票代码】2025-06-09 03:40:28,832 - data_commands - INFO -
```

日志标题与后面的时间戳信息直接相连，不进行换行，这样便于查看和检索。

### 函数级别日志

增强型日志系统现在支持自动检测并显示调用日志的实际函数名和行号，格式为"模块名【函数名】(行号)"。这对于调试和追踪代码执行流程非常有用：

```python
def process_data(data):
    logger = get_unified_logger("my_module")
    logger.debug("开始处理数据")
    # 输出: 【TASK123】2023-06-15 12:34:56 | my_module【process_data】(45)| DEBUG | 开始处理数据
    
    # 处理数据...
    
    logger.info("数据处理完成")
    # 输出: 【TASK123】2023-06-15 12:34:57 | my_module【process_data】(50)| INFO | 数据处理完成
```

#### 函数名显示的特点

1. **自动检测**：系统会自动检测调用日志方法的实际函数名和行号，无需手动指定
2. **跳过内部调用**：系统会跳过日志系统内部的调用，找到实际的用户代码调用
3. **格式统一**：函数名使用【函数名】(行号)格式显示，便于识别和定位
4. **固定宽度显示**：函数名使用固定宽度显示，过长时从左侧截断，保持视觉一致性
5. **支持所有格式化器**：增强型格式化器和表格式Markdown格式化器都支持此功能

## 新功能

### 表格式Markdown日志格式化器

新增了`TableMarkdownFormatter`格式化器，提供以下功能：

1. **表格式布局**：使用竖线(|)分隔各字段，使日志更整洁清晰
2. **模块名右对齐**：模块名设置固定宽度并右对齐，超长时从左侧截断并添加省略号
3. **Markdown层级结构**：使用#、##等符号表示层级结构，直观展示操作的嵌套关系
4. **颜色支持**：可选择是否使用颜色，支持为不同模块和日志级别应用不同颜色
5. **函数名显示**：自动显示调用函数名和行号，格式为【函数名】(行号)，与增强型格式化器一致

使用示例：

```python
from utils.logger import setup_unified_logging, get_unified_logger

# 使用表格式Markdown格式化器初始化日志系统
setup_unified_logging(
    use_enhanced_formatter=False,  # 不使用默认的增强型格式化器
    use_table_formatter=True,      # 使用表格式Markdown格式化器
    module_width=30,               # 设置模块名显示宽度
    function_width=30              # 设置函数名显示宽度
)

# 获取日志记录器并记录日志
logger = get_unified_logger("my_module")
logger.info("这是一条普通日志")
logger.info("开始处理数据")  # 自动增加层级
logger.info("读取文件")      # 自动增加缩进
logger.info("处理完成")      # 自动减少层级
```

输出效果示例：

```
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【test_function】(45)| INFO | # 这是一条测试日志     
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【process_data】(67)| INFO | ## 开始处理数据        
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【read_file】(89)| INFO | ## 读取文件
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【process_data】(120)| INFO | # 处理完成
```

使用表格式Markdown格式化器的优点：

1. **整齐一致的布局**：所有字段按照固定宽度对齐，使日志更易于阅读和理解
2. **清晰的层级结构**：使用Markdown风格的#、##等符号表示层级关系，直观反映操作的嵌套层级
3. **模块名右对齐**：模块名固定宽度并右对齐，超长时从左侧截断，保持视觉一致性
4. **更好的可读性**：使用竖线分隔各字段，即使在不同终端环境下也能保持良好的可读性
5. **函数名显示**：自动显示调用函数名，便于追踪代码执行流程

要启用表格式Markdown格式化器，只需在初始化日志系统时设置`use_table_formatter=True`：

```python
from utils.logger import setup_unified_logging, LogTarget

# 在主程序入口处初始化日志系统
setup_unified_logging(
    default_target=LogTarget.FILE,
    use_table_formatter=True,  # 启用表格式Markdown格式化器
    module_width=30,  # 设置模块名显示宽度为30
    function_width=30  # 设置函数名显示宽度为30
)
```

### 控制初始化消息

日志系统初始化时可以控制是否打印初始化完成消息：

```python
from utils.logger import setup_unified_logging, LogTarget

# 默认不打印初始化消息
setup_unified_logging()

# 显式指定不打印初始化消息
setup_unified_logging(print_init_message=False)

# 显式指定打印初始化消息
setup_unified_logging(print_init_message=True)
```

建议在主程序中设置`print_init_message=True`，而在其他模块中设置`print_init_message=False`，以避免重复打印初始化消息。

## 使用标题和内容

您可以使用title和content参数为日志添加标题和内容：

```python
logger.info(
    title_target=LogTarget.BOTH,
    title="【操作完成】",
    content_target=LogTarget.FILE,
    content="详细数据..."
)
```

这将在控制台和文件中显示标题，但只在文件中显示详细内容。

日志标题格式示例：
```
【当前进度：标准化需要下载的股票代码】2025-06-09 03:40:28,832 - data_commands - INFO -
```

日志标题与后面的时间戳信息直接相连，不进行换行，这样便于查看和检索。